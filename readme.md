# GrapesJS Web App Builder

Build a complete GrapesJS web application that exactly matches the provided screenshot with the following specifications:

## Main Layout Structure

### Left Sidebar (Pages & Layers Panel)
- **Pages Section** at the top with:
  - "Pages" header with a "+" button for adding new pages
  - Three-dot menu button (⋯) for page options
  - "Desktop" dropdown selector with dropdown arrow
  - Web icon with "..." indicating current page

### Layers Panel (Below Pages)
- **Layers hierarchy** showing:
  - "# Layers" header
  - Expandable "Body" layer (with collapse/expand arrow)
  - Nested "Header" layer under Body (with expand arrow)
  - Nested "Div" layer under Body (with expand arrow)
  - **Selected "Heading" layer** (highlighted in blue) containing:
    - Three "Text" components listed vertically

## Main Content Area
- **GrapesJS logo** at the top center
- **Blue highlighted heading** with text: "Welcome to GrapesJS Studio SDK!"
- **Body text paragraphs**:
  - "You're currently viewing the default fallback project for web."
  - "This appears because no storage has been configured yet. To set up your own storage, follow the guide here:"
  - **Pink link**: "https://grap.grapesjs.com/docs-sdk/configuration/project/default-hosted-storage"
  - "Want to customize the fallback project? You can do so by setting options.project.default. Learn more here:"
  - **Pink link**: "https://grap.grapesjs.com/docs-sdk/configuration/project/#setup"
  - "Happy building! 🚀"

## Right Sidebar (Properties Panel)

### Top Section
- **"Styles" tab** (active/selected)
- **"Properties" tab** (inactive)

### Selection Info
- **"Selection" section** showing:
  - Text icon (T) with "Heading" label
  - "+" button
  - "title" text on the right

### Layout Section
- **"Layout" header** with collapse/expand arrow
- **Four layout buttons** in a row:
  - Left align button
  - Center align button  
  - Right align button
  - Justify button

### Collapsible Sections (all with expand/collapse arrows)
- **"Size"** section (collapsed)
- **"Space"** section (collapsed)  
- **"Typography"** section (collapsed)
- **"Background"** section (collapsed)
- **"Borders"** section (collapsed)
- **"Effects"** section (collapsed)

## Top Toolbar (Right Side)
Include these icon buttons from left to right:
- Eye icon (visibility toggle)
- Code view icon (</>)
- Undo arrow
- Redo arrow
- Cloud/export icon
- Settings/cog icon
- Dark/light mode toggle (moon icon)

## Technical Requirements

### GrapesJS Configuration
- Initialize GrapesJS with:
  - Layer manager enabled and visible
  - Pages manager enabled 
  - Property/style manager with all listed sections
  - Responsive design controls (Desktop dropdown)

### Component Structure
- Create the exact component hierarchy shown in layers:
  - Body > Header, Div > Heading > Text elements
- Ensure the heading component is selectable and shows proper properties

### Styling
- Match the exact color scheme:
  - Blue highlight for selected elements
  - Pink/magenta for links
  - Gray sidebar backgrounds
  - Clean white main content area

### Functionality
- All buttons should be functional (layout alignment, expand/collapse)
- Layer selection should work properly
- Property panel should update based on selection
- Include proper icons for all toolbar and interface elements

### Content Population
- Pre-populate with the exact text content shown
- Include the GrapesJS logo
- Set up the default project structure as displayed

## Additional Features
- Responsive design support
- Proper drag-and-drop functionality
- Component selection highlighting
- Expandable/collapsible interface sections
- Working toolbar with all specified icons and functions

Build this as a complete, functional GrapesJS application that matches the screenshot pixel-perfectly with all interactive elements working as expected.