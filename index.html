<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>GrapesJS Web Builder</title>
    
    <!-- GrapesJS CSS -->
    <link rel="stylesheet" href="https://unpkg.com/grapesjs/dist/css/grapes.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Top Toolbar -->
    <div class="toolbar-top">
        <div class="toolbar-left">
            <!-- GrapesJS Logo will be added here -->
        </div>
        <div class="toolbar-right">
            <button class="toolbar-btn" id="visibility-btn" title="Toggle Visibility">
                <i class="fas fa-eye"></i>
            </button>
            <button class="toolbar-btn" id="code-btn" title="Code View">
                <i class="fas fa-code"></i>
            </button>
            <button class="toolbar-btn" id="undo-btn" title="Undo">
                <i class="fas fa-undo"></i>
            </button>
            <button class="toolbar-btn" id="redo-btn" title="Redo">
                <i class="fas fa-redo"></i>
            </button>
            <button class="toolbar-btn" id="export-btn" title="Export">
                <i class="fas fa-cloud-upload-alt"></i>
            </button>
            <button class="toolbar-btn" id="settings-btn" title="Settings">
                <i class="fas fa-cog"></i>
            </button>
            <button class="toolbar-btn" id="theme-btn" title="Toggle Theme">
                <i class="fas fa-moon"></i>
            </button>
        </div>
    </div>

    <!-- Main Editor Container -->
    <div class="editor-container">
        <!-- Left Sidebar -->
        <div class="sidebar-left">
            <!-- Pages Panel -->
            <div class="pages-panel">
                <div class="panel-header">
                    <span class="panel-title">Pages</span>
                    <div class="panel-actions">
                        <button class="btn-icon" id="add-page-btn" title="Add Page">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="btn-icon" id="page-menu-btn" title="Page Menu">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                </div>
                <div class="device-selector">
                    <select id="device-select">
                        <option value="desktop">Desktop</option>
                        <option value="tablet">Tablet</option>
                        <option value="mobile">Mobile</option>
                    </select>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="page-item active">
                    <i class="fas fa-globe"></i>
                    <span>Web</span>
                    <span class="page-indicator">...</span>
                </div>
            </div>

            <!-- Layers Panel -->
            <div class="layers-panel">
                <div class="panel-header">
                    <span class="panel-title"># Layers</span>
                </div>
                <div class="layers-container" id="layers-container">
                    <!-- Layers will be populated by GrapesJS -->
                </div>
            </div>
        </div>

        <!-- Main Canvas Area -->
        <div class="canvas-area">
            <div id="gjs">
                <!-- Default content structure matching the screenshot -->
                <body class="gjs-body">
                    <header class="gjs-header">
                        <div class="gjs-logo-container">
                            <img src="https://grapesjs.com/img/grapesjs-logo.png" alt="GrapesJS" class="gjs-logo">
                            <h1>GrapesJS</h1>
                        </div>
                    </header>

                    <div class="gjs-content-div">
                        <h2 class="welcome-heading" data-gjs-type="heading">
                            Welcome to GrapesJS Studio SDK!
                        </h2>

                        <p>You're currently viewing the default fallback project for web.</p>

                        <p>This appears because no storage has been configured yet. To set up your own storage, follow the guide here:</p>

                        <p><a href="https://grap.grapesjs.com/docs-sdk/configuration/project/default-hosted-storage" class="pink-link">https://grap.grapesjs.com/docs-sdk/configuration/project/default-hosted-storage</a></p>

                        <p>Want to customize the fallback project? You can do so by setting options.project.default. Learn more here:</p>

                        <p><a href="https://grap.grapesjs.com/docs-sdk/configuration/project/#setup" class="pink-link">https://grap.grapesjs.com/docs-sdk/configuration/project/#setup</a></p>

                        <p>Happy building! 🚀</p>
                    </div>
                </body>
            </div>
        </div>

        <!-- Right Sidebar -->
        <div class="sidebar-right">
            <!-- Tabs -->
            <div class="tabs-container">
                <button class="tab-btn active" id="styles-tab">Styles</button>
                <button class="tab-btn" id="properties-tab">Properties</button>
            </div>

            <!-- Selection Info -->
            <div class="selection-info">
                <div class="selection-item">
                    <i class="fas fa-heading"></i>
                    <span>Heading</span>
                    <button class="btn-icon">
                        <i class="fas fa-plus"></i>
                    </button>
                    <span class="selection-value">title</span>
                </div>
            </div>

            <!-- Layout Section -->
            <div class="property-section">
                <div class="section-header">
                    <span class="section-title">Layout</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="layout-controls">
                    <button class="layout-btn" title="Align Left">
                        <i class="fas fa-align-left"></i>
                    </button>
                    <button class="layout-btn" title="Align Center">
                        <i class="fas fa-align-center"></i>
                    </button>
                    <button class="layout-btn" title="Align Right">
                        <i class="fas fa-align-right"></i>
                    </button>
                    <button class="layout-btn" title="Justify">
                        <i class="fas fa-align-justify"></i>
                    </button>
                </div>
            </div>

            <!-- Style Manager Container -->
            <div class="style-manager-container" id="style-manager">
                <!-- Style manager will be populated by GrapesJS -->
            </div>

            <!-- Trait Manager Container -->
            <div class="trait-manager-container" id="trait-manager">
                <!-- Trait manager will be populated by GrapesJS -->
            </div>
        </div>
    </div>

    <!-- GrapesJS JavaScript -->
    <script src="https://unpkg.com/grapesjs"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/app.js"></script>
</body>
</html>
