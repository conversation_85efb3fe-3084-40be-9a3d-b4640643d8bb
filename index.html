<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>GrapesJS Web Builder</title>
    
    <!-- GrapesJS CSS -->
    <link rel="stylesheet" href="https://unpkg.com/grapesjs/dist/css/grapes.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Top Toolbar -->
    <div class="toolbar-top">
        <div class="toolbar-left">
            <span style="font-weight: bold; color: var(--text-primary);">GrapesJS Studio</span>
            <div class="theme-selector">
                <div class="theme-dropdown" id="theme-dropdown">
                    <span id="current-theme">Light</span>
                    <i class="fas fa-chevron-down" style="margin-left: 4px; font-size: 10px;"></i>
                </div>
                <div class="theme-options" id="theme-options">
                    <div class="theme-option active" data-theme="light">Light</div>
                    <div class="theme-option" data-theme="dark">Dark</div>
                    <div class="theme-option" data-theme="blue">Blue</div>
                    <div class="theme-option" data-theme="green">Green</div>
                    <div class="theme-option" data-theme="purple">Purple</div>
                </div>
            </div>
        </div>

        <!-- Center Device Selector -->
        <div class="toolbar-center">
            <div class="device-selector-group">
                <button class="device-btn active" data-device="desktop" title="Desktop View">
                    <i class="fas fa-desktop"></i>
                    <span>Desktop</span>
                </button>
                <button class="device-btn" data-device="tablet" title="Tablet View">
                    <i class="fas fa-tablet-alt"></i>
                    <span>Tablet</span>
                </button>
                <button class="device-btn" data-device="mobile" title="Mobile View">
                    <i class="fas fa-mobile-alt"></i>
                    <span>Mobile</span>
                </button>
            </div>
            <div class="device-label" id="device-label">Desktop View</div>
        </div>

        <div class="toolbar-right">
            <button class="toolbar-btn" id="left-sidebar-toggle" title="Toggle Left Sidebar">
                <i class="fas fa-bars"></i>
            </button>
            <button class="toolbar-btn" id="right-sidebar-toggle" title="Toggle Right Sidebar">
                <i class="fas fa-columns"></i>
            </button>
            <button class="toolbar-btn active" id="visibility-btn" title="Hide Element Borders">
                <i class="fas fa-eye"></i>
            </button>
            <button class="toolbar-btn" id="code-btn" title="Code View">
                <i class="fas fa-code"></i>
            </button>
            <button class="toolbar-btn" id="undo-btn" title="Undo">
                <i class="fas fa-undo"></i>
            </button>
            <button class="toolbar-btn" id="redo-btn" title="Redo">
                <i class="fas fa-redo"></i>
            </button>
            <button class="toolbar-btn" id="export-btn" title="Export">
                <i class="fas fa-cloud-upload-alt"></i>
            </button>
            <button class="toolbar-btn" id="settings-btn" title="Settings">
                <i class="fas fa-cog"></i>
            </button>
            <button class="toolbar-btn" id="theme-btn" title="Toggle Theme">
                <i class="fas fa-moon"></i>
            </button>
        </div>
    </div>

    <!-- Main Editor Container -->
    <div class="editor-container">
        <!-- Left Sidebar -->
        <div class="sidebar-left">
            <!-- Project Type Selector -->
            <div class="project-type-panel">
                <div class="panel-header">
                    <span class="panel-title">Project Type</span>
                </div>
                <div class="project-type-selector">
                    <button class="project-type-btn active" data-type="web" title="Web Builder">
                        <i class="fas fa-globe"></i>
                        <span>Web</span>
                    </button>
                    <button class="project-type-btn" data-type="email" title="Email Builder">
                        <i class="fas fa-envelope"></i>
                        <span>Email</span>
                    </button>
                    <button class="project-type-btn" data-type="document" title="Document Builder">
                        <i class="fas fa-file-alt"></i>
                        <span>Document</span>
                    </button>
                </div>
            </div>

            <!-- Pages Panel (Always visible at top) -->
            <div class="pages-panel">
                <div class="panel-header">
                    <span class="panel-title">Pages</span>
                    <div class="panel-actions">
                        <button class="btn-icon" id="add-page-btn" title="Add Page">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="btn-icon" id="page-menu-btn" title="Page Menu">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                </div>
                <div class="page-item active" id="current-page">
                    <i class="fas fa-globe"></i>
                    <span>Web Page</span>
                    <span class="page-indicator">...</span>
                </div>
            </div>

            <!-- Left Sidebar Tabs -->
            <div class="left-sidebar-tabs">
                <button class="left-tab-btn active" id="layers-tab" data-panel="layers" title="Layers">
                    <i class="fas fa-layer-group"></i>
                </button>
                <button class="left-tab-btn" id="blocks-tab" data-panel="blocks" title="Blocks">
                    <i class="fas fa-th-large"></i>
                </button>
                <button class="left-tab-btn" id="templates-tab" data-panel="templates" title="Templates">
                    <i class="fas fa-file-alt"></i>
                </button>
                <button class="left-tab-btn" id="assets-tab" data-panel="assets" title="Assets">
                    <i class="fas fa-images"></i>
                </button>
            </div>

            <!-- Content Panels -->
            <div class="left-sidebar-content">
                <!-- Layers Panel -->
                <div class="content-panel active" id="layers-panel">
                    <div class="layers-container" id="layers-container">
                        <!-- Layers will be populated by GrapesJS -->
                    </div>
                </div>

                <!-- Blocks Panel -->
                <div class="content-panel" id="blocks-panel">
                    <div class="blocks-container" id="blocks-container">
                        <!-- Blocks will be populated by GrapesJS -->
                    </div>
                </div>

                <!-- Templates Panel (placeholder - will open modal) -->
                <div class="content-panel" id="templates-panel">
                    <div class="templates-placeholder">
                        <div class="placeholder-content">
                            <i class="fas fa-file-alt placeholder-icon"></i>
                            <h3>Templates</h3>
                            <p>Choose from pre-built templates to get started quickly</p>
                            <button class="btn-primary" id="open-templates-modal">Browse Templates</button>
                        </div>
                    </div>
                </div>

                <!-- Assets Panel -->
                <div class="content-panel" id="assets-panel">
                    <div class="assets-container" id="assets-container">
                        <!-- Assets will be populated by GrapesJS -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Canvas Area -->
        <div class="canvas-area">
            <div id="gjs">
                <!-- Default content structure matching the screenshot -->
                <body class="gjs-body">
                    <header class="gjs-header">
                        <div class="gjs-logo-container">
                            <img src="https://grapesjs.com/img/grapesjs-logo.png" alt="GrapesJS" class="gjs-logo">
                            <h1>GrapesJS</h1>
                        </div>
                    </header>

                    <div class="gjs-content-div">
                        <h2 class="welcome-heading">
                            Welcome to GrapesJS Studio SDK!
                        </h2>

                        <p>You're currently viewing the default fallback project for web.</p>

                        <p>This appears because no storage has been configured yet. To set up your own storage, follow the guide here:</p>

                        <p><a href="https://grap.grapesjs.com/docs-sdk/configuration/project/default-hosted-storage" class="pink-link">https://grap.grapesjs.com/docs-sdk/configuration/project/default-hosted-storage</a></p>

                        <p>Want to customize the fallback project? You can do so by setting options.project.default. Learn more here:</p>

                        <p><a href="https://grap.grapesjs.com/docs-sdk/configuration/project/#setup" class="pink-link">https://grap.grapesjs.com/docs-sdk/configuration/project/#setup</a></p>

                        <p>Happy building! 🚀</p>
                    </div>
                </body>
            </div>
        </div>

        <!-- Right Sidebar -->
        <div class="sidebar-right">
            <!-- Tabs -->
            <div class="tabs-container">
                <button class="tab-btn active" id="styles-tab">Styles</button>
                <button class="tab-btn" id="properties-tab">Properties</button>
                <button class="tab-btn" id="global-styles-tab">Global</button>
            </div>

            <!-- Selection Info -->
            <div class="selection-info">
                <div class="selection-item">
                    <i class="fas fa-heading"></i>
                    <span>Heading</span>
                    <button class="btn-icon">
                        <i class="fas fa-plus"></i>
                    </button>
                    <span class="selection-value">title</span>
                </div>
            </div>

            <!-- Layout Section -->
            <div class="property-section">
                <div class="section-header">
                    <span class="section-title">Layout</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="layout-controls">
                    <button class="layout-btn" title="Align Left">
                        <i class="fas fa-align-left"></i>
                    </button>
                    <button class="layout-btn" title="Align Center">
                        <i class="fas fa-align-center"></i>
                    </button>
                    <button class="layout-btn" title="Align Right">
                        <i class="fas fa-align-right"></i>
                    </button>
                    <button class="layout-btn" title="Justify">
                        <i class="fas fa-align-justify"></i>
                    </button>
                </div>
            </div>

            <!-- Style Manager Container -->
            <div class="style-manager-container" id="style-manager">
                <!-- Style manager will be populated by GrapesJS -->
            </div>

            <!-- Trait Manager Container -->
            <div class="trait-manager-container" id="trait-manager">
                <!-- Trait manager will be populated by GrapesJS -->
            </div>

            <!-- Global Styles Container -->
            <div class="global-styles-container" id="global-styles" style="display: none;">
                <div class="global-styles-content">
                    <!-- Global styles will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Context Menu -->
    <div class="context-menu" id="context-menu">
        <div class="context-menu-item" data-action="copy">
            <i class="fas fa-copy context-menu-icon"></i>
            <span>Copy</span>
            <span class="context-menu-shortcut">Ctrl+C</span>
        </div>
        <div class="context-menu-item" data-action="cut">
            <i class="fas fa-cut context-menu-icon"></i>
            <span>Cut</span>
            <span class="context-menu-shortcut">Ctrl+X</span>
        </div>
        <div class="context-menu-item" data-action="paste">
            <i class="fas fa-paste context-menu-icon"></i>
            <span>Paste</span>
            <span class="context-menu-shortcut">Ctrl+V</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="duplicate">
            <i class="fas fa-clone context-menu-icon"></i>
            <span>Duplicate</span>
            <span class="context-menu-shortcut">Ctrl+D</span>
        </div>
        <div class="context-menu-item" data-action="delete">
            <i class="fas fa-trash context-menu-icon"></i>
            <span>Delete</span>
            <span class="context-menu-shortcut">Del</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="move-up">
            <i class="fas fa-arrow-up context-menu-icon"></i>
            <span>Move Up</span>
        </div>
        <div class="context-menu-item" data-action="move-down">
            <i class="fas fa-arrow-down context-menu-icon"></i>
            <span>Move Down</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="edit-code">
            <i class="fas fa-code context-menu-icon"></i>
            <span>Edit Code</span>
        </div>
        <div class="context-menu-item" data-action="inspect">
            <i class="fas fa-search context-menu-icon"></i>
            <span>Inspect Element</span>
        </div>
    </div>

    <!-- GrapesJS JavaScript -->
    <script src="https://unpkg.com/grapesjs"></script>

    <!-- Custom JavaScript -->
    <script src="js/app.js"></script>
</body>
</html>
