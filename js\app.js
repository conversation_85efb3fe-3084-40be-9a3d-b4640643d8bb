// GrapesJS Web Builder Application
document.addEventListener('DOMContentLoaded', function() {

    // Prevent extension-related errors from affecting the app
    window.addEventListener('error', function(e) {
        if (e.message && e.message.includes('message channel closed')) {
            e.preventDefault();
            return false;
        }
    });

    window.addEventListener('unhandledrejection', function(e) {
        if (e.reason && e.reason.message && e.reason.message.includes('message channel closed')) {
            e.preventDefault();
            return false;
        }
    });
    
    // Initialize GrapesJS Editor with error handling
    let editor;

    try {
        editor = grapesjs.init({
        container: '#gjs',
        height: '100%',
        width: 'auto',
        
        // Storage configuration
        storageManager: {
            type: 'local',
            autosave: true,
            autoload: true,
            stepsBeforeSave: 1,
            options: {
                local: {
                    key: 'grapesjs-web-builder'
                }
            }
        },
        
        // Layer Manager
        layerManager: {
            appendTo: '#layers-container'
        },
        
        // Style Manager
        styleManager: {
            appendTo: '#style-manager',
            sectors: [
                {
                    name: 'Size',
                    open: false,
                    buildProps: ['width', 'height', 'min-width', 'min-height', 'max-width', 'max-height', 'padding', 'margin']
                },
                {
                    name: 'Space',
                    open: false,
                    buildProps: ['padding-top', 'padding-right', 'padding-bottom', 'padding-left', 'margin-top', 'margin-right', 'margin-bottom', 'margin-left']
                },
                {
                    name: 'Typography',
                    open: false,
                    buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-align', 'text-decoration', 'text-shadow']
                },
                {
                    name: 'Background',
                    open: false,
                    buildProps: ['background-color', 'background-image', 'background-repeat', 'background-position', 'background-attachment', 'background-size']
                },
                {
                    name: 'Borders',
                    open: false,
                    buildProps: ['border-top-width', 'border-right-width', 'border-bottom-width', 'border-left-width', 'border-top-style', 'border-right-style', 'border-bottom-style', 'border-left-style', 'border-top-color', 'border-right-color', 'border-bottom-color', 'border-left-color', 'border-radius']
                },
                {
                    name: 'Effects',
                    open: false,
                    buildProps: ['opacity', 'box-shadow', 'filter']
                }
            ]
        },
        
        // Trait Manager
        traitManager: {
            appendTo: '#trait-manager'
        },
        
        // Device Manager for responsive design
        deviceManager: {
            devices: [
                {
                    name: 'Desktop',
                    width: ''
                },
                {
                    name: 'Tablet',
                    width: '768px',
                    widthMedia: '992px'
                },
                {
                    name: 'Mobile',
                    width: '320px',
                    widthMedia: '768px'
                }
            ]
        },
        
        // Block Manager
        blockManager: {
            appendTo: null // We'll handle blocks separately
        },
        
        // Panels configuration
        panels: {
            defaults: []
        },
        
        // Canvas configuration
        canvas: {
            styles: [
                'css/style.css'
            ]
        },
        
        // From element configuration
        fromElement: true
    });

    } catch (error) {
        console.error('Error initializing GrapesJS:', error);
        document.getElementById('gjs').innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Error loading editor. Please refresh the page.</div>';
        return;
    }

    if (!editor) {
        console.error('Failed to initialize GrapesJS editor');
        return;
    }

    // Make editor globally available for debugging
    window.editor = editor;

    // Add custom commands for better control
    editor.Commands.add('toggle-borders', {
        run: function(editor, sender) {
            const canvas = editor.Canvas.getElement();
            const isVisible = canvas.classList.contains('gjs-dashed');

            if (isVisible) {
                canvas.classList.remove('gjs-dashed');
                sender.set('active', false);
            } else {
                canvas.classList.add('gjs-dashed');
                sender.set('active', true);
            }
        }
    });

    // Add custom command for component visibility
    editor.Commands.add('toggle-component-visibility', {
        run: function(editor, _sender, options = {}) {
            const { component } = options;
            if (!component) return;

            // Use GrapesJS's built-in visibility methods
            const currentStyle = component.get('style') || {};
            const isVisible = currentStyle.display !== 'none';

            if (isVisible) {
                // Hide component
                component.set('style', { ...currentStyle, display: 'none' });
            } else {
                // Show component - remove display none
                const newStyle = { ...currentStyle };
                delete newStyle.display;
                component.set('style', newStyle);
            }

            // Force view refresh
            const view = component.getView();
            if (view) {
                view.render();
            }

            // Trigger updates
            component.trigger('change:style');
            editor.trigger('component:update', component);
            editor.Canvas.getCanvasView().updateFrames();

            return !isVisible; // Return new visibility state
        }
    });

    // Initialize default content structure to match the screenshot exactly
    function initializeDefaultContent() {
        // Wait for editor to be fully loaded
        setTimeout(() => {
            const wrapper = editor.DomComponents.getWrapper();

            // Find and select the heading to show proper hierarchy in layers
            const heading = wrapper.find('.welcome-heading')[0];
            if (heading) {
                // Add the title trait to the heading
                heading.addTrait({
                    type: 'text',
                    name: 'title',
                    label: 'Title',
                    value: 'title'
                });

                // Select the heading to show it in the layers panel
                editor.select(heading);

                // Ensure the heading has the proper structure for the layers panel
                // Add text components if they don't exist
                if (heading.components().length === 0) {
                    heading.components([
                        {
                            tagName: 'span',
                            type: 'text',
                            content: 'Text 1',
                            attributes: { class: 'text-component' }
                        },
                        {
                            tagName: 'span',
                            type: 'text',
                            content: 'Text 2',
                            attributes: { class: 'text-component' }
                        },
                        {
                            tagName: 'span',
                            type: 'text',
                            content: 'Text 3',
                            attributes: { class: 'text-component' }
                        }
                    ]);
                }
            }

            // Ensure proper layer names are displayed
            updateLayerNames();
        }, 500);
    }

    // Function to update layer names to match the screenshot
    function updateLayerNames() {
        const wrapper = editor.DomComponents.getWrapper();

        // Find components and set proper names and IDs for the layer manager
        const body = wrapper.find('body')[0];
        if (body) {
            body.set('name', 'Body');
            body.addAttributes({ 'data-layer-id': 'body-layer' });
        }

        const header = wrapper.find('header')[0];
        if (header) {
            header.set('name', 'Header');
            header.addAttributes({ 'data-layer-id': 'header-layer' });
        }

        const contentDiv = wrapper.find('.gjs-content-div')[0];
        if (contentDiv) {
            contentDiv.set('name', 'Div');
            contentDiv.addAttributes({ 'data-layer-id': 'div-layer' });
        }

        const heading = wrapper.find('.welcome-heading')[0];
        if (heading) {
            heading.set('name', 'Heading');
            heading.addAttributes({ 'data-layer-id': 'heading-layer' });
        }

        // Set names for text components
        const textComponents = wrapper.find('p');
        textComponents.forEach((comp, index) => {
            comp.set('name', 'Text');
            comp.addAttributes({ 'data-layer-id': `text-layer-${index}` });
        });
    }

    // Toolbar button handlers
    function initializeToolbarHandlers() {
        // Visibility toggle - properly handle show/hide borders
        document.getElementById('visibility-btn').addEventListener('click', function() {
            const canvas = editor.Canvas.getElement();
            const isVisible = canvas.classList.contains('gjs-dashed');

            if (isVisible) {
                // Hide borders
                canvas.classList.remove('gjs-dashed');
                this.classList.remove('active');
                this.title = 'Show Element Borders';

                // Also remove any outline styles
                const iframeDoc = editor.Canvas.getDocument();
                if (iframeDoc) {
                    const style = iframeDoc.getElementById('gjs-visibility-style');
                    if (style) style.remove();
                }
            } else {
                // Show borders
                canvas.classList.add('gjs-dashed');
                this.classList.add('active');
                this.title = 'Hide Element Borders';

                // Add outline styles to iframe document
                const iframeDoc = editor.Canvas.getDocument();
                if (iframeDoc) {
                    let style = iframeDoc.getElementById('gjs-visibility-style');
                    if (!style) {
                        style = iframeDoc.createElement('style');
                        style.id = 'gjs-visibility-style';
                        style.textContent = `
                            * { outline: 1px dashed rgba(170, 170, 170, 0.7) !important; }
                            *:hover { outline: 2px solid #007bff !important; }
                        `;
                        iframeDoc.head.appendChild(style);
                    }
                }
            }
        });
        
        // Code view
        document.getElementById('code-btn').addEventListener('click', function() {
            editor.runCommand('core:open-code');
        });
        
        // Undo
        document.getElementById('undo-btn').addEventListener('click', function() {
            editor.runCommand('core:undo');
        });
        
        // Redo
        document.getElementById('redo-btn').addEventListener('click', function() {
            editor.runCommand('core:redo');
        });
        
        // Export
        document.getElementById('export-btn').addEventListener('click', function() {
            const html = editor.getHtml();
            const css = editor.getCss();
            const exportContent = `<!DOCTYPE html>
<html>
<head>
    <style>${css}</style>
</head>
<body>
    ${html}
</body>
</html>`;
            
            const blob = new Blob([exportContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'grapesjs-export.html';
            a.click();
            URL.revokeObjectURL(url);
        });
        
        // Settings
        document.getElementById('settings-btn').addEventListener('click', function() {
            // Open settings modal
            editor.Modal.setTitle('Settings')
                .setContent('<div style="padding: 20px;">Settings panel coming soon...</div>')
                .open();
        });
        
        // Theme toggle
        document.getElementById('theme-btn').addEventListener('click', function() {
            document.body.classList.toggle('dark-theme');
            const icon = this.querySelector('i');
            icon.classList.toggle('fa-moon');
            icon.classList.toggle('fa-sun');
        });
    }

    // Device selector handler
    function initializeDeviceSelector() {
        const deviceSelect = document.getElementById('device-select');
        deviceSelect.addEventListener('change', function() {
            editor.setDevice(this.value.charAt(0).toUpperCase() + this.value.slice(1));
        });
    }

    // Pages panel handlers
    function initializePagesPanel() {
        // Add page button
        document.getElementById('add-page-btn').addEventListener('click', function() {
            // For now, just show a message
            editor.Modal.setTitle('Add Page')
                .setContent('<div style="padding: 20px;">Add page functionality coming soon...</div>')
                .open();
        });

        // Page menu button
        document.getElementById('page-menu-btn').addEventListener('click', function() {
            // Show page options menu
            editor.Modal.setTitle('Page Options')
                .setContent('<div style="padding: 20px;">Page options coming soon...</div>')
                .open();
        });
    }

    // Tab switching
    function initializeTabSwitching() {
        const stylesTab = document.getElementById('styles-tab');
        const propertiesTab = document.getElementById('properties-tab');
        const styleManager = document.getElementById('style-manager');
        const traitManager = document.getElementById('trait-manager');
        
        stylesTab.addEventListener('click', function() {
            stylesTab.classList.add('active');
            propertiesTab.classList.remove('active');
            styleManager.style.display = 'block';
            traitManager.style.display = 'none';
        });
        
        propertiesTab.addEventListener('click', function() {
            propertiesTab.classList.add('active');
            stylesTab.classList.remove('active');
            styleManager.style.display = 'none';
            traitManager.style.display = 'block';
        });
        
        // Initially show styles
        styleManager.style.display = 'block';
        traitManager.style.display = 'none';
    }

    // Layout control handlers
    function initializeLayoutControls() {
        const layoutBtns = document.querySelectorAll('.layout-btn');
        layoutBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                layoutBtns.forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');
                
                // Apply text alignment based on button
                const selected = editor.getSelected();
                if (selected) {
                    const icon = this.querySelector('i');
                    if (icon.classList.contains('fa-align-left')) {
                        selected.addStyle({ 'text-align': 'left' });
                    } else if (icon.classList.contains('fa-align-center')) {
                        selected.addStyle({ 'text-align': 'center' });
                    } else if (icon.classList.contains('fa-align-right')) {
                        selected.addStyle({ 'text-align': 'right' });
                    } else if (icon.classList.contains('fa-align-justify')) {
                        selected.addStyle({ 'text-align': 'justify' });
                    }
                }
            });
        });
    }

    // Initialize all components
    editor.on('load', function() {
        initializeDefaultContent();
        initializeToolbarHandlers();
        initializeDeviceSelector();
        initializePagesPanel();
        initializeTabSwitching();
        initializeLayoutControls();
        initializeSectionCollapse();
        initializeLayerVisibility();

        // Add simple event delegation for layer visibility with component tracking
        const componentVisibilityMap = new Map();

        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('gjs-lm-vis')) {
                e.stopPropagation();
                e.preventDefault();

                const layerEl = e.target.closest('.gjs-lm-layer');
                if (layerEl) {
                    const layerName = layerEl.querySelector('.gjs-lm-layer-name')?.textContent?.trim();
                    console.log('Clicked visibility for layer:', layerName);

                    // Get or find component
                    let component = componentVisibilityMap.get(layerName);

                    if (!component) {
                        // Find and cache the component
                        const wrapper = editor.DomComponents.getWrapper();

                        switch(layerName?.toLowerCase()) {
                            case 'body':
                                component = wrapper.find('body')[0] || wrapper.find('.gjs-body')[0];
                                break;
                            case 'header':
                                component = wrapper.find('header')[0] || wrapper.find('.gjs-header')[0];
                                break;
                            case 'div':
                                component = wrapper.find('.gjs-content-div')[0];
                                break;
                            case 'heading':
                                component = wrapper.find('.welcome-heading')[0] || wrapper.find('h2')[0];
                                break;
                            case 'text':
                                // For text, we need to handle multiple text elements
                                const paragraphs = wrapper.find('p');
                                if (paragraphs.length > 0) {
                                    // Find which text element this layer represents
                                    const textLayers = Array.from(layerEl.parentElement.children).filter(el =>
                                        el.querySelector('.gjs-lm-layer-name')?.textContent?.trim().toLowerCase() === 'text'
                                    );
                                    const textIndex = textLayers.indexOf(layerEl);
                                    component = paragraphs[textIndex] || paragraphs[0];
                                }
                                break;
                        }

                        if (component) {
                            componentVisibilityMap.set(layerName, component);
                        }
                    }

                    if (component) {
                        // Use the custom command for reliable visibility toggle
                        const newVisibilityState = editor.runCommand('toggle-component-visibility', { component });

                        // Update layer appearance
                        if (newVisibilityState) {
                            // Component is now visible
                            layerEl.classList.remove('gjs-lm-hide');
                            e.target.title = 'Hide element';
                            console.log(`${layerName} is now visible`);
                        } else {
                            // Component is now hidden
                            layerEl.classList.add('gjs-lm-hide');
                            e.target.title = 'Show element';
                            console.log(`${layerName} is now hidden`);
                        }

                        // Debug logging
                        setTimeout(() => {
                            const finalStyles = component.get('style') || {};
                            console.log(`Final styles for ${layerName}:`, finalStyles);
                        }, 100);

                    } else {
                        console.error(`Could not find component for layer: ${layerName}`);
                    }
                }
            }
        });

        // Set initial visibility state
        const visibilityBtn = document.getElementById('visibility-btn');
        const canvas = editor.Canvas.getElement();
        canvas.classList.add('gjs-dashed');
        visibilityBtn.classList.add('active');
        visibilityBtn.title = 'Hide Element Borders';

        console.log('GrapesJS Web Builder initialized successfully!');
    });

    // Update selection info when component is selected
    editor.on('component:selected', function(component) {
        const selectionInfo = document.querySelector('.selection-item span:first-of-type');
        const selectionValue = document.querySelector('.selection-value');

        if (component) {
            const tagName = component.get('tagName');
            const className = component.getClasses().join(' ');
            const componentName = component.get('name') || tagName;

            selectionInfo.textContent = componentName.charAt(0).toUpperCase() + componentName.slice(1);
            selectionValue.textContent = className || component.get('type') || 'title';

            // Update layout button states based on component styles
            updateLayoutButtonStates(component);
        }
    });

    // Function to update layout button states
    function updateLayoutButtonStates(component) {
        const layoutBtns = document.querySelectorAll('.layout-btn');
        const textAlign = component.getStyle()['text-align'] || 'left';

        layoutBtns.forEach(btn => btn.classList.remove('active'));

        const alignmentMap = {
            'left': 'fa-align-left',
            'center': 'fa-align-center',
            'right': 'fa-align-right',
            'justify': 'fa-align-justify'
        };

        const targetIcon = alignmentMap[textAlign];
        if (targetIcon) {
            const targetBtn = document.querySelector(`.layout-btn i.${targetIcon}`)?.parentElement;
            if (targetBtn) {
                targetBtn.classList.add('active');
            }
        }
    }

    // Add section collapse/expand functionality
    function initializeSectionCollapse() {
        // Handle style manager section collapse
        editor.on('styleManager:sector:toggle', function(sector) {
            const sectorEl = sector.getEl();
            if (sectorEl) {
                sectorEl.classList.toggle('gjs-sm-open');
            }
        });

        // Handle custom property section collapse
        document.addEventListener('click', function(e) {
            if (e.target.closest('.section-header')) {
                const header = e.target.closest('.section-header');
                const section = header.parentElement;
                const content = section.querySelector('.layout-controls');

                if (content) {
                    const isCollapsed = content.style.display === 'none';
                    content.style.display = isCollapsed ? 'flex' : 'none';
                    header.classList.toggle('collapsed', !isCollapsed);
                }
            }
        });
    }

    // Initialize layer visibility functionality
    function initializeLayerVisibility() {
        // Use MutationObserver to watch for layer changes
        const layersContainer = document.getElementById('layers-container');

        if (layersContainer) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                addVisibilityToLayers(node);
                            }
                        });
                    }
                });
            });

            observer.observe(layersContainer, {
                childList: true,
                subtree: true
            });

            // Add visibility to existing layers
            setTimeout(() => {
                addVisibilityToLayers(layersContainer);
            }, 1500);
        }

        function addVisibilityToLayers(container) {
            const layers = container.querySelectorAll('.gjs-lm-layer');

            layers.forEach(layerEl => {
                if (!layerEl.querySelector('.gjs-lm-vis')) {
                    const titleEl = layerEl.querySelector('.gjs-lm-title');
                    if (titleEl) {
                        const visToggle = document.createElement('div');
                        visToggle.className = 'gjs-lm-vis';
                        visToggle.title = 'Toggle visibility';

                        titleEl.appendChild(visToggle);

                        // Add click handler
                        visToggle.addEventListener('click', function(e) {
                            e.stopPropagation();
                            e.preventDefault();

                            // Find the component associated with this layer
                            const layerName = layerEl.querySelector('.gjs-lm-layer-name')?.textContent;
                            const wrapper = editor.DomComponents.getWrapper();

                            // Try to find component by various methods
                            let component = null;

                            // Method 1: Find by layer element data
                            const layerId = layerEl.getAttribute('data-layer-id');
                            if (layerId) {
                                component = wrapper.find(`#${layerId}`)[0];
                            }

                            // Method 2: Find by tag name or class
                            if (!component && layerName) {
                                if (layerName.toLowerCase() === 'body') {
                                    component = wrapper.find('body')[0] || wrapper.find('.gjs-body')[0];
                                } else if (layerName.toLowerCase() === 'header') {
                                    component = wrapper.find('header')[0] || wrapper.find('.gjs-header')[0];
                                } else if (layerName.toLowerCase() === 'div') {
                                    component = wrapper.find('.gjs-content-div')[0];
                                } else if (layerName.toLowerCase() === 'heading') {
                                    component = wrapper.find('.welcome-heading')[0] || wrapper.find('h2')[0];
                                } else if (layerName.toLowerCase() === 'text') {
                                    // For text elements, find the first text component that's not hidden
                                    const textComponents = wrapper.find('p');
                                    component = textComponents.find(comp => comp.getStyle().display !== 'none') || textComponents[0];
                                }
                            }

                            if (component) {
                                const currentDisplay = component.getStyle().display;
                                const isHidden = currentDisplay === 'none';

                                if (isHidden) {
                                    // Show the component
                                    component.removeStyle('display');
                                    layerEl.classList.remove('gjs-lm-hide');
                                    visToggle.title = 'Hide element';
                                } else {
                                    // Hide the component
                                    component.addStyle({ display: 'none' });
                                    layerEl.classList.add('gjs-lm-hide');
                                    visToggle.title = 'Show element';
                                }

                                // Force canvas refresh
                                editor.refresh();
                                console.log(`Toggled visibility for ${layerName}:`, !isHidden ? 'hidden' : 'visible');
                            } else {
                                console.warn(`Could not find component for layer: ${layerName}`);
                            }
                        });
                    }
                }
            });
        }
    }
});
