// GrapesJS Web Builder Application
document.addEventListener('DOMContentLoaded', function() {

    // Prevent extension-related errors from affecting the app
    window.addEventListener('error', function(e) {
        if (e.message && e.message.includes('message channel closed')) {
            e.preventDefault();
            return false;
        }
    });

    window.addEventListener('unhandledrejection', function(e) {
        if (e.reason && e.reason.message && e.reason.message.includes('message channel closed')) {
            e.preventDefault();
            return false;
        }
    });
    
    // Initialize GrapesJS Editor with error handling
    let editor;

    try {
        editor = grapesjs.init({
        container: '#gjs',
        height: '100%',
        width: 'auto',
        
        // Storage configuration
        storageManager: {
            type: 'local',
            autosave: true,
            autoload: true,
            stepsBeforeSave: 1,
            options: {
                local: {
                    key: 'grapesjs-web-builder'
                }
            }
        },
        
        // Layer Manager
        layerManager: {
            appendTo: '#layers-container'
        },
        
        // Style Manager
        styleManager: {
            appendTo: '#style-manager',
            sectors: [
                {
                    name: 'Size',
                    open: false,
                    buildProps: ['width', 'height', 'min-width', 'min-height', 'max-width', 'max-height', 'padding', 'margin']
                },
                {
                    name: 'Space',
                    open: false,
                    buildProps: ['padding-top', 'padding-right', 'padding-bottom', 'padding-left', 'margin-top', 'margin-right', 'margin-bottom', 'margin-left']
                },
                {
                    name: 'Typography',
                    open: false,
                    buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-align', 'text-decoration', 'text-shadow']
                },
                {
                    name: 'Background',
                    open: false,
                    buildProps: ['background-color', 'background-image', 'background-repeat', 'background-position', 'background-attachment', 'background-size']
                },
                {
                    name: 'Borders',
                    open: false,
                    buildProps: ['border-top-width', 'border-right-width', 'border-bottom-width', 'border-left-width', 'border-top-style', 'border-right-style', 'border-bottom-style', 'border-left-style', 'border-top-color', 'border-right-color', 'border-bottom-color', 'border-left-color', 'border-radius']
                },
                {
                    name: 'Effects',
                    open: false,
                    buildProps: ['opacity', 'box-shadow', 'filter']
                }
            ]
        },
        
        // Trait Manager
        traitManager: {
            appendTo: '#trait-manager'
        },
        
        // Device Manager for responsive design
        deviceManager: {
            devices: [
                {
                    name: 'Desktop',
                    width: ''
                },
                {
                    name: 'Tablet',
                    width: '768px',
                    widthMedia: '992px'
                },
                {
                    name: 'Mobile',
                    width: '320px',
                    widthMedia: '768px'
                }
            ]
        },
        
        // Block Manager
        blockManager: {
            appendTo: '#blocks-container'
        },
        
        // Panels configuration
        panels: {
            defaults: []
        },
        
        // Canvas configuration
        canvas: {
            styles: [
                'css/style.css'
            ]
        },

        // From element configuration
        fromElement: true,

        // Avoid component type errors
        avoidInlineStyle: false,
        avoidFrameOffset: true
    });

    } catch (error) {
        console.error('Error initializing GrapesJS:', error);
        document.getElementById('gjs').innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Error loading editor. Please refresh the page.</div>';
        return;
    }

    if (!editor) {
        console.error('Failed to initialize GrapesJS editor');
        return;
    }

    // Make editor globally available for debugging
    window.editor = editor;

    // Add custom commands for better control
    editor.Commands.add('toggle-borders', {
        run: function(editor, sender) {
            const canvas = editor.Canvas.getElement();
            const isVisible = canvas.classList.contains('gjs-dashed');

            if (isVisible) {
                canvas.classList.remove('gjs-dashed');
                sender.set('active', false);
            } else {
                canvas.classList.add('gjs-dashed');
                sender.set('active', true);
            }
        }
    });

    // Add custom command for component visibility
    editor.Commands.add('toggle-component-visibility', {
        run: function(editor, _sender, options = {}) {
            const { component } = options;
            if (!component) return;

            // Use GrapesJS's built-in visibility methods
            const currentStyle = component.get('style') || {};
            const isVisible = currentStyle.display !== 'none';

            if (isVisible) {
                // Hide component
                component.set('style', { ...currentStyle, display: 'none' });
            } else {
                // Show component - remove display none
                const newStyle = { ...currentStyle };
                delete newStyle.display;
                component.set('style', newStyle);
            }

            // Force view refresh
            const view = component.getView();
            if (view) {
                view.render();
            }

            // Trigger updates
            component.trigger('change:style');
            editor.trigger('component:update', component);
            editor.Canvas.getCanvasView().updateFrames();

            return !isVisible; // Return new visibility state
        }
    });

    // Initialize default content structure to match the screenshot exactly
    function initializeDefaultContent() {
        // Wait for editor to be fully loaded
        setTimeout(() => {
            const wrapper = editor.DomComponents.getWrapper();

            // Find and select the heading to show proper hierarchy in layers
            const heading = wrapper.find('.welcome-heading')[0];
            if (heading) {
                // Select the heading to show it in the layers panel
                editor.select(heading);
            }

            // Ensure proper layer names are displayed
            updateLayerNames();
        }, 500);
    }

    // Function to update layer names to match the screenshot
    function updateLayerNames() {
        const wrapper = editor.DomComponents.getWrapper();

        // Find components and set proper names and IDs for the layer manager
        const body = wrapper.find('body')[0];
        if (body) {
            body.set('name', 'Body');
            body.addAttributes({ 'data-layer-id': 'body-layer' });
        }

        const header = wrapper.find('header')[0];
        if (header) {
            header.set('name', 'Header');
            header.addAttributes({ 'data-layer-id': 'header-layer' });
        }

        const contentDiv = wrapper.find('.gjs-content-div')[0];
        if (contentDiv) {
            contentDiv.set('name', 'Div');
            contentDiv.addAttributes({ 'data-layer-id': 'div-layer' });
        }

        const heading = wrapper.find('.welcome-heading')[0];
        if (heading) {
            heading.set('name', 'Heading');
            heading.addAttributes({ 'data-layer-id': 'heading-layer' });
        }

        // Set names for text components
        const textComponents = wrapper.find('p');
        textComponents.forEach((comp, index) => {
            comp.set('name', 'Text');
            comp.addAttributes({ 'data-layer-id': `text-layer-${index}` });
        });
    }

    // Toolbar button handlers
    function initializeToolbarHandlers() {
        // Visibility toggle - properly handle show/hide borders
        document.getElementById('visibility-btn').addEventListener('click', function() {
            const canvas = editor.Canvas.getElement();
            const isVisible = canvas.classList.contains('gjs-dashed');

            if (isVisible) {
                // Hide borders
                canvas.classList.remove('gjs-dashed');
                this.classList.remove('active');
                this.title = 'Show Element Borders';

                // Also remove any outline styles
                const iframeDoc = editor.Canvas.getDocument();
                if (iframeDoc) {
                    const style = iframeDoc.getElementById('gjs-visibility-style');
                    if (style) style.remove();
                }
            } else {
                // Show borders
                canvas.classList.add('gjs-dashed');
                this.classList.add('active');
                this.title = 'Hide Element Borders';

                // Add outline styles to iframe document
                const iframeDoc = editor.Canvas.getDocument();
                if (iframeDoc) {
                    let style = iframeDoc.getElementById('gjs-visibility-style');
                    if (!style) {
                        style = iframeDoc.createElement('style');
                        style.id = 'gjs-visibility-style';
                        style.textContent = `
                            * { outline: 1px dashed rgba(170, 170, 170, 0.7) !important; }
                            *:hover { outline: 2px solid #007bff !important; }
                        `;
                        iframeDoc.head.appendChild(style);
                    }
                }
            }
        });
        
        // Code view
        document.getElementById('code-btn').addEventListener('click', function() {
            editor.runCommand('core:open-code');
        });
        
        // Undo
        document.getElementById('undo-btn').addEventListener('click', function() {
            editor.runCommand('core:undo');
        });
        
        // Redo
        document.getElementById('redo-btn').addEventListener('click', function() {
            editor.runCommand('core:redo');
        });
        
        // Export
        document.getElementById('export-btn').addEventListener('click', function() {
            const html = editor.getHtml();
            const css = editor.getCss();
            const exportContent = `<!DOCTYPE html>
<html>
<head>
    <style>${css}</style>
</head>
<body>
    ${html}
</body>
</html>`;
            
            const blob = new Blob([exportContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'grapesjs-export.html';
            a.click();
            URL.revokeObjectURL(url);
        });
        
        // Settings
        document.getElementById('settings-btn').addEventListener('click', function() {
            // Open settings modal
            editor.Modal.setTitle('Settings')
                .setContent('<div style="padding: 20px;">Settings panel coming soon...</div>')
                .open();
        });
        
        // Theme toggle (legacy dark mode)
        document.getElementById('theme-btn').addEventListener('click', function() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);
        });
    }

    // Device selector handler
    function initializeDeviceSelector() {
        const deviceSelect = document.getElementById('device-select');
        deviceSelect.addEventListener('change', function() {
            editor.setDevice(this.value.charAt(0).toUpperCase() + this.value.slice(1));
        });
    }

    // Pages panel handlers
    function initializePagesPanel() {
        // Add page button
        document.getElementById('add-page-btn').addEventListener('click', function() {
            // For now, just show a message
            editor.Modal.setTitle('Add Page')
                .setContent('<div style="padding: 20px;">Add page functionality coming soon...</div>')
                .open();
        });

        // Page menu button
        document.getElementById('page-menu-btn').addEventListener('click', function() {
            // Show page options menu
            editor.Modal.setTitle('Page Options')
                .setContent('<div style="padding: 20px;">Page options coming soon...</div>')
                .open();
        });
    }

    // Tab switching
    function initializeTabSwitching() {
        const stylesTab = document.getElementById('styles-tab');
        const propertiesTab = document.getElementById('properties-tab');
        const globalStylesTab = document.getElementById('global-styles-tab');
        const styleManager = document.getElementById('style-manager');
        const traitManager = document.getElementById('trait-manager');
        const globalStyles = document.getElementById('global-styles');

        function showTab(activeTab, activePanel) {
            // Remove active class from all tabs
            [stylesTab, propertiesTab, globalStylesTab].forEach(tab => tab.classList.remove('active'));
            // Hide all panels
            [styleManager, traitManager, globalStyles].forEach(panel => panel.style.display = 'none');

            // Show active tab and panel
            activeTab.classList.add('active');
            activePanel.style.display = 'block';
        }

        stylesTab.addEventListener('click', function() {
            showTab(stylesTab, styleManager);
        });

        propertiesTab.addEventListener('click', function() {
            showTab(propertiesTab, traitManager);
        });

        globalStylesTab.addEventListener('click', function() {
            showTab(globalStylesTab, globalStyles);
        });

        // Initially show styles
        showTab(stylesTab, styleManager);
    }

    // Global Styles System
    function initializeGlobalStyles() {
        const globalStylesContainer = document.querySelector('.global-styles-content');

        const globalStylesHTML = `
            <div class="global-styles-section">
                <div class="global-styles-header">
                    <span>Typography</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="global-styles-content">
                    <div class="global-style-group">
                        <label class="global-style-label">Primary Font Family</label>
                        <select class="global-style-input" id="primary-font">
                            <option value="system">System Default</option>
                            <option value="Arial, sans-serif">Arial</option>
                            <option value="Helvetica, sans-serif">Helvetica</option>
                            <option value="Georgia, serif">Georgia</option>
                            <option value="'Times New Roman', serif">Times New Roman</option>
                            <option value="'Courier New', monospace">Courier New</option>
                        </select>
                    </div>
                    <div class="global-style-group">
                        <label class="global-style-label">Base Font Size</label>
                        <div class="global-style-row">
                            <input type="range" class="global-style-input" id="base-font-size" min="12" max="24" value="16">
                            <span id="font-size-value">16px</span>
                        </div>
                    </div>
                    <div class="global-style-group">
                        <label class="global-style-label">Line Height</label>
                        <input type="number" class="global-style-input" id="line-height" value="1.5" step="0.1" min="1" max="3">
                    </div>
                </div>
            </div>

            <div class="global-styles-section">
                <div class="global-styles-header">
                    <span>Colors</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="global-styles-content">
                    <div class="global-style-group">
                        <label class="global-style-label">Primary Color</label>
                        <div class="global-style-row">
                            <input type="color" class="color-picker" id="primary-color" value="#007bff">
                            <input type="text" class="global-style-input" id="primary-color-text" value="#007bff">
                        </div>
                        <div class="global-style-preset">
                            <div class="preset-item" style="background: #007bff;" data-color="#007bff"></div>
                            <div class="preset-item" style="background: #28a745;" data-color="#28a745"></div>
                            <div class="preset-item" style="background: #dc3545;" data-color="#dc3545"></div>
                            <div class="preset-item" style="background: #ffc107;" data-color="#ffc107"></div>
                            <div class="preset-item" style="background: #6f42c1;" data-color="#6f42c1"></div>
                        </div>
                    </div>
                    <div class="global-style-group">
                        <label class="global-style-label">Secondary Color</label>
                        <div class="global-style-row">
                            <input type="color" class="color-picker" id="secondary-color" value="#6c757d">
                            <input type="text" class="global-style-input" id="secondary-color-text" value="#6c757d">
                        </div>
                    </div>
                    <div class="global-style-group">
                        <label class="global-style-label">Text Color</label>
                        <div class="global-style-row">
                            <input type="color" class="color-picker" id="text-color" value="#333333">
                            <input type="text" class="global-style-input" id="text-color-text" value="#333333">
                        </div>
                    </div>
                </div>
            </div>

            <div class="global-styles-section">
                <div class="global-styles-header">
                    <span>Spacing</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="global-styles-content">
                    <div class="global-style-group">
                        <label class="global-style-label">Container Max Width</label>
                        <div class="global-style-row">
                            <input type="range" class="global-style-input" id="container-width" min="800" max="1400" value="1200" step="50">
                            <span id="container-width-value">1200px</span>
                        </div>
                    </div>
                    <div class="global-style-group">
                        <label class="global-style-label">Section Padding</label>
                        <div class="global-style-row">
                            <input type="range" class="global-style-input" id="section-padding" min="10" max="100" value="40" step="5">
                            <span id="section-padding-value">40px</span>
                        </div>
                    </div>
                    <div class="global-style-group">
                        <label class="global-style-label">Element Margin</label>
                        <div class="global-style-row">
                            <input type="range" class="global-style-input" id="element-margin" min="5" max="50" value="15" step="5">
                            <span id="element-margin-value">15px</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="global-style-group" style="margin-top: 20px;">
                <button class="global-style-button" id="apply-global-styles">Apply Global Styles</button>
                <button class="global-style-button secondary" id="reset-global-styles">Reset to Defaults</button>
            </div>
        `;

        globalStylesContainer.innerHTML = globalStylesHTML;

        // Initialize event handlers
        initializeGlobalStylesHandlers();
    }

    function initializeGlobalStylesHandlers() {
        // Font size slider
        const fontSizeSlider = document.getElementById('base-font-size');
        const fontSizeValue = document.getElementById('font-size-value');
        fontSizeSlider.addEventListener('input', function() {
            fontSizeValue.textContent = this.value + 'px';
        });

        // Container width slider
        const containerWidthSlider = document.getElementById('container-width');
        const containerWidthValue = document.getElementById('container-width-value');
        containerWidthSlider.addEventListener('input', function() {
            containerWidthValue.textContent = this.value + 'px';
        });

        // Section padding slider
        const sectionPaddingSlider = document.getElementById('section-padding');
        const sectionPaddingValue = document.getElementById('section-padding-value');
        sectionPaddingSlider.addEventListener('input', function() {
            sectionPaddingValue.textContent = this.value + 'px';
        });

        // Element margin slider
        const elementMarginSlider = document.getElementById('element-margin');
        const elementMarginValue = document.getElementById('element-margin-value');
        elementMarginSlider.addEventListener('input', function() {
            elementMarginValue.textContent = this.value + 'px';
        });

        // Color pickers sync
        ['primary', 'secondary', 'text'].forEach(colorType => {
            const colorPicker = document.getElementById(`${colorType}-color`);
            const colorText = document.getElementById(`${colorType}-color-text`);

            colorPicker.addEventListener('change', function() {
                colorText.value = this.value;
            });

            colorText.addEventListener('change', function() {
                colorPicker.value = this.value;
            });
        });

        // Color presets
        document.querySelectorAll('.preset-item').forEach(preset => {
            preset.addEventListener('click', function() {
                const color = this.getAttribute('data-color');
                document.getElementById('primary-color').value = color;
                document.getElementById('primary-color-text').value = color;
            });
        });

        // Apply global styles
        document.getElementById('apply-global-styles').addEventListener('click', applyGlobalStyles);

        // Reset global styles
        document.getElementById('reset-global-styles').addEventListener('click', resetGlobalStyles);

        // Section collapse/expand
        document.querySelectorAll('.global-styles-header').forEach(header => {
            header.addEventListener('click', function() {
                const content = this.nextElementSibling;
                const icon = this.querySelector('i');

                if (content.style.display === 'none') {
                    content.style.display = 'block';
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    content.style.display = 'none';
                    icon.style.transform = 'rotate(-90deg)';
                }
            });
        });
    }

    function applyGlobalStyles() {
        const styles = {
            fontFamily: document.getElementById('primary-font').value,
            fontSize: document.getElementById('base-font-size').value + 'px',
            lineHeight: document.getElementById('line-height').value,
            primaryColor: document.getElementById('primary-color').value,
            secondaryColor: document.getElementById('secondary-color').value,
            textColor: document.getElementById('text-color').value,
            containerWidth: document.getElementById('container-width').value + 'px',
            sectionPadding: document.getElementById('section-padding').value + 'px',
            elementMargin: document.getElementById('element-margin').value + 'px'
        };

        // Apply styles to the canvas
        const canvas = editor.Canvas.getDocument();
        if (canvas) {
            let globalCSS = `
                body {
                    font-family: ${styles.fontFamily} !important;
                    font-size: ${styles.fontSize} !important;
                    line-height: ${styles.lineHeight} !important;
                    color: ${styles.textColor} !important;
                }
                .container, .gjs-content-div {
                    max-width: ${styles.containerWidth} !important;
                    margin: 0 auto !important;
                }
                section {
                    padding: ${styles.sectionPadding} 20px !important;
                }
                h1, h2, h3, h4, h5, h6, p, div {
                    margin-bottom: ${styles.elementMargin} !important;
                }
                .btn, button {
                    background-color: ${styles.primaryColor} !important;
                }
                a {
                    color: ${styles.primaryColor} !important;
                }
            `;

            // Add or update global styles
            let styleEl = canvas.getElementById('global-styles');
            if (!styleEl) {
                styleEl = canvas.createElement('style');
                styleEl.id = 'global-styles';
                canvas.head.appendChild(styleEl);
            }
            styleEl.textContent = globalCSS;
        }

        // Save global styles
        localStorage.setItem('grapesjs-global-styles', JSON.stringify(styles));

        console.log('Global styles applied:', styles);
    }

    function resetGlobalStyles() {
        if (confirm('Reset all global styles to defaults?')) {
            // Reset form values
            document.getElementById('primary-font').value = 'system';
            document.getElementById('base-font-size').value = '16';
            document.getElementById('font-size-value').textContent = '16px';
            document.getElementById('line-height').value = '1.5';
            document.getElementById('primary-color').value = '#007bff';
            document.getElementById('primary-color-text').value = '#007bff';
            document.getElementById('secondary-color').value = '#6c757d';
            document.getElementById('secondary-color-text').value = '#6c757d';
            document.getElementById('text-color').value = '#333333';
            document.getElementById('text-color-text').value = '#333333';
            document.getElementById('container-width').value = '1200';
            document.getElementById('container-width-value').textContent = '1200px';
            document.getElementById('section-padding').value = '40';
            document.getElementById('section-padding-value').textContent = '40px';
            document.getElementById('element-margin').value = '15';
            document.getElementById('element-margin-value').textContent = '15px';

            // Remove global styles from canvas
            const canvas = editor.Canvas.getDocument();
            if (canvas) {
                const styleEl = canvas.getElementById('global-styles');
                if (styleEl) {
                    styleEl.remove();
                }
            }

            // Clear saved styles
            localStorage.removeItem('grapesjs-global-styles');

            console.log('Global styles reset to defaults');
        }
    }

    // Layout control handlers
    function initializeLayoutControls() {
        const layoutBtns = document.querySelectorAll('.layout-btn');
        layoutBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                layoutBtns.forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');
                
                // Apply text alignment based on button
                const selected = editor.getSelected();
                if (selected) {
                    const icon = this.querySelector('i');
                    if (icon.classList.contains('fa-align-left')) {
                        selected.addStyle({ 'text-align': 'left' });
                    } else if (icon.classList.contains('fa-align-center')) {
                        selected.addStyle({ 'text-align': 'center' });
                    } else if (icon.classList.contains('fa-align-right')) {
                        selected.addStyle({ 'text-align': 'right' });
                    } else if (icon.classList.contains('fa-align-justify')) {
                        selected.addStyle({ 'text-align': 'justify' });
                    }
                }
            });
        });
    }

    // Initialize comprehensive blocks
    function initializeBlocks() {
        const blockManager = editor.BlockManager;

        // Layout Blocks
        blockManager.add('section', {
            label: 'Section',
            category: 'Layout',
            content: `<section style="padding: 20px; margin: 10px 0;">
                <h2>Section Title</h2>
                <p>Section content goes here...</p>
            </section>`,
            attributes: { class: 'gjs-block-section' }
        });

        blockManager.add('container', {
            label: 'Container',
            category: 'Layout',
            content: `<div style="max-width: 1200px; margin: 0 auto; padding: 0 15px;">
                <p>Container content</p>
            </div>`,
            attributes: { class: 'gjs-block-container' }
        });

        blockManager.add('row', {
            label: 'Row',
            category: 'Layout',
            content: `<div style="display: flex; flex-wrap: wrap; margin: 0 -15px;">
                <div style="flex: 1; padding: 0 15px;">Column 1</div>
                <div style="flex: 1; padding: 0 15px;">Column 2</div>
            </div>`,
            attributes: { class: 'gjs-block-row' }
        });

        blockManager.add('column', {
            label: 'Column',
            category: 'Layout',
            content: `<div style="flex: 1; padding: 15px; border: 1px dashed #ccc;">
                Column content
            </div>`,
            attributes: { class: 'gjs-block-column' }
        });

        // Content Blocks
        blockManager.add('text', {
            label: 'Text',
            category: 'Content',
            content: '<div data-gjs-type="text">Insert your text here</div>',
            attributes: { class: 'gjs-block-text' }
        });

        blockManager.add('heading', {
            label: 'Heading',
            category: 'Content',
            content: '<h1>Heading Text</h1>',
            attributes: { class: 'gjs-block-heading' }
        });

        blockManager.add('paragraph', {
            label: 'Paragraph',
            category: 'Content',
            content: '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>',
            attributes: { class: 'gjs-block-paragraph' }
        });

        blockManager.add('list', {
            label: 'List',
            category: 'Content',
            content: `<ul>
                <li>List item 1</li>
                <li>List item 2</li>
                <li>List item 3</li>
            </ul>`,
            attributes: { class: 'gjs-block-list' }
        });

        blockManager.add('quote', {
            label: 'Quote',
            category: 'Content',
            content: `<blockquote style="border-left: 4px solid #007bff; padding-left: 15px; margin: 20px 0; font-style: italic;">
                "This is a quote block"
            </blockquote>`,
            attributes: { class: 'gjs-block-quote' }
        });

        // Media Blocks
        blockManager.add('image', {
            label: 'Image',
            category: 'Media',
            content: { type: 'image' },
            select: true,
            activate: true,
            attributes: { class: 'gjs-block-image' }
        });

        blockManager.add('video', {
            label: 'Video',
            category: 'Media',
            content: `<video controls style="width: 100%; max-width: 100%;">
                <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>`,
            attributes: { class: 'gjs-block-video' }
        });

        blockManager.add('iframe', {
            label: 'Embed',
            category: 'Media',
            content: `<iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                style="width: 100%; height: 315px; border: none;"
                frameborder="0" allowfullscreen></iframe>`,
            attributes: { class: 'gjs-block-iframe' }
        });

        // Form Blocks
        blockManager.add('form', {
            label: 'Form',
            category: 'Forms',
            content: `<form style="padding: 20px; border: 1px solid #ddd; border-radius: 4px;">
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px;">Name:</label>
                    <input type="text" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px;">Email:</label>
                    <input type="email" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                </div>
                <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">Submit</button>
            </form>`,
            attributes: { class: 'gjs-block-form' }
        });

        blockManager.add('input', {
            label: 'Input',
            category: 'Forms',
            content: '<input type="text" placeholder="Enter text..." style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">',
            attributes: { class: 'gjs-block-input' }
        });

        blockManager.add('textarea', {
            label: 'Textarea',
            category: 'Forms',
            content: '<textarea placeholder="Enter your message..." style="width: 100%; height: 100px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; resize: vertical;"></textarea>',
            attributes: { class: 'gjs-block-textarea' }
        });

        blockManager.add('button', {
            label: 'Button',
            category: 'Forms',
            content: '<button style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">Click Me</button>',
            attributes: { class: 'gjs-block-button' }
        });

        // Navigation Blocks
        blockManager.add('navbar', {
            label: 'Navbar',
            category: 'Navigation',
            content: `<nav style="background: #333; padding: 10px 0;">
                <div style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center; padding: 0 15px;">
                    <div style="color: white; font-weight: bold;">Brand</div>
                    <div>
                        <a href="#" style="color: white; text-decoration: none; margin: 0 15px;">Home</a>
                        <a href="#" style="color: white; text-decoration: none; margin: 0 15px;">About</a>
                        <a href="#" style="color: white; text-decoration: none; margin: 0 15px;">Contact</a>
                    </div>
                </div>
            </nav>`,
            attributes: { class: 'gjs-block-navbar' }
        });

        blockManager.add('breadcrumb', {
            label: 'Breadcrumb',
            category: 'Navigation',
            content: `<nav style="padding: 10px 0;">
                <a href="#" style="color: #007bff; text-decoration: none;">Home</a>
                <span style="margin: 0 5px;">/</span>
                <a href="#" style="color: #007bff; text-decoration: none;">Category</a>
                <span style="margin: 0 5px;">/</span>
                <span style="color: #666;">Current Page</span>
            </nav>`,
            attributes: { class: 'gjs-block-breadcrumb' }
        });
    }

    // Theme Management System
    function initializeThemeSystem() {
        const themeDropdown = document.getElementById('theme-dropdown');
        const themeOptions = document.getElementById('theme-options');
        const currentThemeSpan = document.getElementById('current-theme');

        // Load saved theme
        const savedTheme = localStorage.getItem('grapesjs-theme') || 'light';
        setTheme(savedTheme);

        // Theme dropdown toggle
        themeDropdown.addEventListener('click', function(e) {
            e.stopPropagation();
            themeOptions.classList.toggle('show');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function() {
            themeOptions.classList.remove('show');
        });

        // Theme option selection
        themeOptions.addEventListener('click', function(e) {
            if (e.target.classList.contains('theme-option')) {
                const theme = e.target.getAttribute('data-theme');
                setTheme(theme);
                themeOptions.classList.remove('show');
            }
        });
    }

    function setTheme(theme) {
        // Update document theme
        document.documentElement.setAttribute('data-theme', theme);

        // Update current theme display
        const currentThemeSpan = document.getElementById('current-theme');
        if (currentThemeSpan) {
            currentThemeSpan.textContent = theme.charAt(0).toUpperCase() + theme.slice(1);
        }

        // Update active theme option
        document.querySelectorAll('.theme-option').forEach(option => {
            option.classList.remove('active');
            if (option.getAttribute('data-theme') === theme) {
                option.classList.add('active');
            }
        });

        // Update theme toggle button icon
        const themeBtn = document.getElementById('theme-btn');
        const icon = themeBtn?.querySelector('i');
        if (icon) {
            icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        // Save theme preference
        localStorage.setItem('grapesjs-theme', theme);

        // Apply theme to canvas iframe if available
        setTimeout(() => {
            const canvas = editor?.Canvas?.getDocument();
            if (canvas) {
                canvas.documentElement.setAttribute('data-theme', theme);
            }
        }, 100);

        console.log(`Theme changed to: ${theme}`);
    }

    // Template Library System
    function initializeTemplateLibrary() {
        const templatesContainer = document.getElementById('templates-container');

        const templates = [
            {
                category: 'Landing Pages',
                items: [
                    {
                        name: 'Hero Landing',
                        description: 'Landing page with hero section',
                        icon: '🚀',
                        content: `
                            <section style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 80px 20px; text-align: center;">
                                <div style="max-width: 800px; margin: 0 auto;">
                                    <h1 style="font-size: 3rem; margin-bottom: 20px; font-weight: bold;">Welcome to Our Platform</h1>
                                    <p style="font-size: 1.2rem; margin-bottom: 30px; opacity: 0.9;">Build amazing websites with our powerful tools and features</p>
                                    <button style="background: #ff6b6b; color: white; padding: 15px 30px; border: none; border-radius: 25px; font-size: 1.1rem; cursor: pointer;">Get Started</button>
                                </div>
                            </section>
                            <section style="padding: 60px 20px;">
                                <div style="max-width: 1200px; margin: 0 auto; display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                                    <div style="text-align: center; padding: 20px;">
                                        <h3>Feature One</h3>
                                        <p>Description of your amazing feature</p>
                                    </div>
                                    <div style="text-align: center; padding: 20px;">
                                        <h3>Feature Two</h3>
                                        <p>Description of your amazing feature</p>
                                    </div>
                                    <div style="text-align: center; padding: 20px;">
                                        <h3>Feature Three</h3>
                                        <p>Description of your amazing feature</p>
                                    </div>
                                </div>
                            </section>`
                    },
                    {
                        name: 'Product Showcase',
                        description: 'Product landing with features',
                        icon: '📱',
                        content: `
                            <header style="background: #2c3e50; color: white; padding: 20px;">
                                <nav style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                                    <div style="font-size: 1.5rem; font-weight: bold;">ProductName</div>
                                    <div>
                                        <a href="#" style="color: white; text-decoration: none; margin: 0 15px;">Features</a>
                                        <a href="#" style="color: white; text-decoration: none; margin: 0 15px;">Pricing</a>
                                        <a href="#" style="color: white; text-decoration: none; margin: 0 15px;">Contact</a>
                                    </div>
                                </nav>
                            </header>
                            <section style="padding: 80px 20px; background: #ecf0f1;">
                                <div style="max-width: 1200px; margin: 0 auto; display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                                    <div>
                                        <h1 style="font-size: 2.5rem; margin-bottom: 20px; color: #2c3e50;">Revolutionary Product</h1>
                                        <p style="font-size: 1.1rem; margin-bottom: 30px; color: #7f8c8d;">Transform your workflow with our innovative solution</p>
                                        <button style="background: #3498db; color: white; padding: 12px 24px; border: none; border-radius: 5px; font-size: 1rem;">Learn More</button>
                                    </div>
                                    <div style="background: #bdc3c7; height: 300px; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #7f8c8d;">
                                        Product Image
                                    </div>
                                </div>
                            </section>`
                    }
                ]
            },
            {
                category: 'Business',
                items: [
                    {
                        name: 'Corporate',
                        description: 'Professional business layout',
                        icon: '🏢',
                        content: `
                            <header style="background: #34495e; color: white; padding: 15px 0;">
                                <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
                                    <h1 style="margin: 0; font-size: 1.8rem;">Corporate Solutions</h1>
                                </div>
                            </header>
                            <section style="padding: 60px 20px;">
                                <div style="max-width: 1200px; margin: 0 auto;">
                                    <h2 style="text-align: center; margin-bottom: 40px; color: #2c3e50;">Our Services</h2>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px;">
                                        <div style="padding: 30px; border: 1px solid #bdc3c7; border-radius: 8px; text-align: center;">
                                            <h3 style="color: #34495e;">Consulting</h3>
                                            <p>Expert business consulting services</p>
                                        </div>
                                        <div style="padding: 30px; border: 1px solid #bdc3c7; border-radius: 8px; text-align: center;">
                                            <h3 style="color: #34495e;">Development</h3>
                                            <p>Custom software development</p>
                                        </div>
                                        <div style="padding: 30px; border: 1px solid #bdc3c7; border-radius: 8px; text-align: center;">
                                            <h3 style="color: #34495e;">Support</h3>
                                            <p>24/7 technical support</p>
                                        </div>
                                    </div>
                                </div>
                            </section>`
                    },
                    {
                        name: 'Agency Portfolio',
                        description: 'Creative agency showcase',
                        icon: '🎨',
                        content: `
                            <section style="background: #1a1a1a; color: white; padding: 100px 20px; text-align: center;">
                                <h1 style="font-size: 4rem; margin-bottom: 20px; font-weight: 300;">Creative Agency</h1>
                                <p style="font-size: 1.3rem; opacity: 0.8;">We create beautiful digital experiences</p>
                            </section>
                            <section style="padding: 80px 20px;">
                                <div style="max-width: 1200px; margin: 0 auto;">
                                    <h2 style="text-align: center; margin-bottom: 50px; font-size: 2.5rem; color: #333;">Our Work</h2>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                                        <div style="background: #f8f9fa; height: 250px; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #6c757d;">Project 1</div>
                                        <div style="background: #f8f9fa; height: 250px; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #6c757d;">Project 2</div>
                                        <div style="background: #f8f9fa; height: 250px; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #6c757d;">Project 3</div>
                                    </div>
                                </div>
                            </section>`
                    }
                ]
            },
            {
                category: 'Blog & Content',
                items: [
                    {
                        name: 'Blog Layout',
                        description: 'Clean blog template',
                        icon: '📝',
                        content: `
                            <header style="background: #fff; border-bottom: 1px solid #e9ecef; padding: 20px 0;">
                                <div style="max-width: 800px; margin: 0 auto; padding: 0 20px;">
                                    <h1 style="margin: 0; color: #343a40;">My Blog</h1>
                                    <p style="margin: 5px 0 0 0; color: #6c757d;">Thoughts and ideas</p>
                                </div>
                            </header>
                            <main style="max-width: 800px; margin: 40px auto; padding: 0 20px;">
                                <article style="margin-bottom: 40px; padding-bottom: 40px; border-bottom: 1px solid #e9ecef;">
                                    <h2 style="color: #343a40; margin-bottom: 10px;">Blog Post Title</h2>
                                    <p style="color: #6c757d; font-size: 0.9rem; margin-bottom: 20px;">Published on March 15, 2024</p>
                                    <p style="line-height: 1.6; color: #495057;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                                    <a href="#" style="color: #007bff; text-decoration: none;">Read more →</a>
                                </article>
                                <article style="margin-bottom: 40px;">
                                    <h2 style="color: #343a40; margin-bottom: 10px;">Another Blog Post</h2>
                                    <p style="color: #6c757d; font-size: 0.9rem; margin-bottom: 20px;">Published on March 10, 2024</p>
                                    <p style="line-height: 1.6; color: #495057;">Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                                    <a href="#" style="color: #007bff; text-decoration: none;">Read more →</a>
                                </article>
                            </main>`
                    }
                ]
            }
        ];

        // Render templates
        let html = '';
        templates.forEach(category => {
            html += `<div class="template-category">${category.category}</div>`;
            category.items.forEach(template => {
                html += `
                    <div class="template-item" data-template="${template.name}">
                        <div class="template-preview">${template.icon}</div>
                        <div class="template-info">
                            <div class="template-name">${template.name}</div>
                            <div class="template-description">${template.description}</div>
                        </div>
                    </div>
                `;
            });
        });

        templatesContainer.innerHTML = html;

        // Add click handlers
        templatesContainer.addEventListener('click', function(e) {
            const templateItem = e.target.closest('.template-item');
            if (templateItem) {
                const templateName = templateItem.getAttribute('data-template');
                const template = templates
                    .flatMap(cat => cat.items)
                    .find(t => t.name === templateName);

                if (template) {
                    loadTemplate(template);
                }
            }
        });

        // Template menu handler
        document.getElementById('template-menu-btn').addEventListener('click', function() {
            editor.Modal.setTitle('Template Options')
                .setContent(`
                    <div style="padding: 20px;">
                        <h4>Template Actions</h4>
                        <button onclick="saveCurrentAsTemplate()" style="margin: 5px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px;">Save Current as Template</button>
                        <button onclick="importTemplate()" style="margin: 5px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px;">Import Template</button>
                        <button onclick="exportTemplates()" style="margin: 5px; padding: 8px 16px; background: #17a2b8; color: white; border: none; border-radius: 4px;">Export Templates</button>
                    </div>
                `)
                .open();
        });
    }

    function loadTemplate(template) {
        if (confirm(`Load "${template.name}" template? This will replace the current content.`)) {
            editor.setComponents(template.content);
            editor.setStyle('');
            console.log(`Template "${template.name}" loaded successfully`);
        }
    }

    // Context Menu System
    function initializeContextMenu() {
        const contextMenu = document.getElementById('context-menu');
        let currentComponent = null;

        // Show context menu on right click in canvas
        editor.on('canvas:contextmenu', function(e) {
            e.preventDefault();
            const component = editor.getSelected();
            if (component) {
                currentComponent = component;
                showContextMenu(e.clientX, e.clientY);
            }
        });

        // Hide context menu on click outside
        document.addEventListener('click', function() {
            hideContextMenu();
        });

        // Handle context menu actions
        contextMenu.addEventListener('click', function(e) {
            const action = e.target.closest('.context-menu-item')?.getAttribute('data-action');
            if (action && currentComponent) {
                handleContextAction(action, currentComponent);
            }
            hideContextMenu();
        });

        function showContextMenu(x, y) {
            contextMenu.style.display = 'block';
            contextMenu.style.left = x + 'px';
            contextMenu.style.top = y + 'px';

            // Adjust position if menu goes off screen
            const rect = contextMenu.getBoundingClientRect();
            if (rect.right > window.innerWidth) {
                contextMenu.style.left = (x - rect.width) + 'px';
            }
            if (rect.bottom > window.innerHeight) {
                contextMenu.style.top = (y - rect.height) + 'px';
            }
        }

        function hideContextMenu() {
            contextMenu.style.display = 'none';
        }

        function handleContextAction(action, component) {
            switch (action) {
                case 'copy':
                    editor.runCommand('core:copy', { component });
                    break;
                case 'duplicate':
                    const parent = component.parent();
                    if (parent) {
                        const clone = component.clone();
                        parent.append(clone);
                        editor.select(clone);
                    }
                    break;
                case 'delete':
                    if (confirm('Delete this component?')) {
                        component.remove();
                    }
                    break;
                case 'inspect':
                    editor.Modal.setTitle('Component Inspector')
                        .setContent(`
                            <div style="padding: 20px; font-family: monospace; font-size: 12px;">
                                <h4>Component Details:</h4>
                                <p><strong>Type:</strong> ${component.get('tagName')}</p>
                                <p><strong>Classes:</strong> ${component.getClasses().join(', ') || 'None'}</p>
                                <p><strong>ID:</strong> ${component.getId() || 'None'}</p>
                                <p><strong>Styles:</strong> ${JSON.stringify(component.getStyle(), null, 2)}</p>
                                <p><strong>Content:</strong> ${component.get('content') || 'None'}</p>
                                <p><strong>Children:</strong> ${component.components().length}</p>
                            </div>
                        `)
                        .open();
                    break;
            }
        }
    }

    // Initialize all components
    editor.on('load', function() {
        initializeBlocks();
        initializeThemeSystem();
        initializeTemplateLibrary();
        initializeGlobalStyles();
        initializeContextMenu();
        initializeDefaultContent();
        initializeToolbarHandlers();
        initializeDeviceSelector();
        initializePagesPanel();
        initializeTabSwitching();
        initializeLayoutControls();
        initializeSectionCollapse();
        initializeLayerVisibility();

        // Add simple event delegation for layer visibility with component tracking
        const componentVisibilityMap = new Map();

        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('gjs-lm-vis')) {
                e.stopPropagation();
                e.preventDefault();

                const layerEl = e.target.closest('.gjs-lm-layer');
                if (layerEl) {
                    const layerName = layerEl.querySelector('.gjs-lm-layer-name')?.textContent?.trim();
                    console.log('Clicked visibility for layer:', layerName);

                    // Get or find component
                    let component = componentVisibilityMap.get(layerName);

                    if (!component) {
                        // Find and cache the component
                        const wrapper = editor.DomComponents.getWrapper();

                        switch(layerName?.toLowerCase()) {
                            case 'body':
                                component = wrapper.find('body')[0] || wrapper.find('.gjs-body')[0];
                                break;
                            case 'header':
                                component = wrapper.find('header')[0] || wrapper.find('.gjs-header')[0];
                                break;
                            case 'div':
                                component = wrapper.find('.gjs-content-div')[0];
                                break;
                            case 'heading':
                                component = wrapper.find('.welcome-heading')[0] || wrapper.find('h2')[0];
                                break;
                            case 'text':
                                // For text, we need to handle multiple text elements
                                const paragraphs = wrapper.find('p');
                                if (paragraphs.length > 0) {
                                    // Find which text element this layer represents
                                    const textLayers = Array.from(layerEl.parentElement.children).filter(el =>
                                        el.querySelector('.gjs-lm-layer-name')?.textContent?.trim().toLowerCase() === 'text'
                                    );
                                    const textIndex = textLayers.indexOf(layerEl);
                                    component = paragraphs[textIndex] || paragraphs[0];
                                }
                                break;
                        }

                        if (component) {
                            componentVisibilityMap.set(layerName, component);
                        }
                    }

                    if (component) {
                        // Use the custom command for reliable visibility toggle
                        const newVisibilityState = editor.runCommand('toggle-component-visibility', { component });

                        // Update layer appearance
                        if (newVisibilityState) {
                            // Component is now visible
                            layerEl.classList.remove('gjs-lm-hide');
                            e.target.title = 'Hide element';
                            console.log(`${layerName} is now visible`);
                        } else {
                            // Component is now hidden
                            layerEl.classList.add('gjs-lm-hide');
                            e.target.title = 'Show element';
                            console.log(`${layerName} is now hidden`);
                        }

                        // Debug logging
                        setTimeout(() => {
                            const finalStyles = component.get('style') || {};
                            console.log(`Final styles for ${layerName}:`, finalStyles);
                        }, 100);

                    } else {
                        console.error(`Could not find component for layer: ${layerName}`);
                    }
                }
            }
        });

        // Set initial visibility state
        const visibilityBtn = document.getElementById('visibility-btn');
        const canvas = editor.Canvas.getElement();
        canvas.classList.add('gjs-dashed');
        visibilityBtn.classList.add('active');
        visibilityBtn.title = 'Hide Element Borders';

        console.log('GrapesJS Web Builder initialized successfully!');
    });

    // Update selection info when component is selected
    editor.on('component:selected', function(component) {
        const selectionInfo = document.querySelector('.selection-item span:first-of-type');
        const selectionValue = document.querySelector('.selection-value');

        if (component) {
            const tagName = component.get('tagName');
            const className = component.getClasses().join(' ');
            const componentName = component.get('name') || tagName;

            selectionInfo.textContent = componentName.charAt(0).toUpperCase() + componentName.slice(1);
            selectionValue.textContent = className || component.get('type') || 'title';

            // Update layout button states based on component styles
            updateLayoutButtonStates(component);
        }
    });

    // Function to update layout button states
    function updateLayoutButtonStates(component) {
        const layoutBtns = document.querySelectorAll('.layout-btn');
        const textAlign = component.getStyle()['text-align'] || 'left';

        layoutBtns.forEach(btn => btn.classList.remove('active'));

        const alignmentMap = {
            'left': 'fa-align-left',
            'center': 'fa-align-center',
            'right': 'fa-align-right',
            'justify': 'fa-align-justify'
        };

        const targetIcon = alignmentMap[textAlign];
        if (targetIcon) {
            const targetBtn = document.querySelector(`.layout-btn i.${targetIcon}`)?.parentElement;
            if (targetBtn) {
                targetBtn.classList.add('active');
            }
        }
    }

    // Add section collapse/expand functionality
    function initializeSectionCollapse() {
        // Handle style manager section collapse
        editor.on('styleManager:sector:toggle', function(sector) {
            const sectorEl = sector.getEl();
            if (sectorEl) {
                sectorEl.classList.toggle('gjs-sm-open');
            }
        });

        // Handle custom property section collapse
        document.addEventListener('click', function(e) {
            if (e.target.closest('.section-header')) {
                const header = e.target.closest('.section-header');
                const section = header.parentElement;
                const content = section.querySelector('.layout-controls');

                if (content) {
                    const isCollapsed = content.style.display === 'none';
                    content.style.display = isCollapsed ? 'flex' : 'none';
                    header.classList.toggle('collapsed', !isCollapsed);
                }
            }
        });
    }

    // Initialize layer visibility functionality
    function initializeLayerVisibility() {
        // Use MutationObserver to watch for layer changes
        const layersContainer = document.getElementById('layers-container');

        if (layersContainer) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                addVisibilityToLayers(node);
                            }
                        });
                    }
                });
            });

            observer.observe(layersContainer, {
                childList: true,
                subtree: true
            });

            // Add visibility to existing layers
            setTimeout(() => {
                addVisibilityToLayers(layersContainer);
            }, 1500);
        }

        function addVisibilityToLayers(container) {
            const layers = container.querySelectorAll('.gjs-lm-layer');

            layers.forEach(layerEl => {
                if (!layerEl.querySelector('.gjs-lm-vis')) {
                    const titleEl = layerEl.querySelector('.gjs-lm-title');
                    if (titleEl) {
                        const visToggle = document.createElement('div');
                        visToggle.className = 'gjs-lm-vis';
                        visToggle.title = 'Toggle visibility';

                        titleEl.appendChild(visToggle);

                        // Add click handler
                        visToggle.addEventListener('click', function(e) {
                            e.stopPropagation();
                            e.preventDefault();

                            // Find the component associated with this layer
                            const layerName = layerEl.querySelector('.gjs-lm-layer-name')?.textContent;
                            const wrapper = editor.DomComponents.getWrapper();

                            // Try to find component by various methods
                            let component = null;

                            // Method 1: Find by layer element data
                            const layerId = layerEl.getAttribute('data-layer-id');
                            if (layerId) {
                                component = wrapper.find(`#${layerId}`)[0];
                            }

                            // Method 2: Find by tag name or class
                            if (!component && layerName) {
                                if (layerName.toLowerCase() === 'body') {
                                    component = wrapper.find('body')[0] || wrapper.find('.gjs-body')[0];
                                } else if (layerName.toLowerCase() === 'header') {
                                    component = wrapper.find('header')[0] || wrapper.find('.gjs-header')[0];
                                } else if (layerName.toLowerCase() === 'div') {
                                    component = wrapper.find('.gjs-content-div')[0];
                                } else if (layerName.toLowerCase() === 'heading') {
                                    component = wrapper.find('.welcome-heading')[0] || wrapper.find('h2')[0];
                                } else if (layerName.toLowerCase() === 'text') {
                                    // For text elements, find the first text component that's not hidden
                                    const textComponents = wrapper.find('p');
                                    component = textComponents.find(comp => comp.getStyle().display !== 'none') || textComponents[0];
                                }
                            }

                            if (component) {
                                const currentDisplay = component.getStyle().display;
                                const isHidden = currentDisplay === 'none';

                                if (isHidden) {
                                    // Show the component
                                    component.removeStyle('display');
                                    layerEl.classList.remove('gjs-lm-hide');
                                    visToggle.title = 'Hide element';
                                } else {
                                    // Hide the component
                                    component.addStyle({ display: 'none' });
                                    layerEl.classList.add('gjs-lm-hide');
                                    visToggle.title = 'Show element';
                                }

                                // Force canvas refresh
                                editor.refresh();
                                console.log(`Toggled visibility for ${layerName}:`, !isHidden ? 'hidden' : 'visible');
                            } else {
                                console.warn(`Could not find component for layer: ${layerName}`);
                            }
                        });
                    }
                }
            });
        }
    }
});
