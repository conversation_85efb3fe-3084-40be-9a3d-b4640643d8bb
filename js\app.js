// GrapesJS Web Builder Application
document.addEventListener('DOMContentLoaded', function() {

    // Prevent extension-related errors from affecting the app
    window.addEventListener('error', function(e) {
        if (e.message && e.message.includes('message channel closed')) {
            e.preventDefault();
            return false;
        }
    });

    window.addEventListener('unhandledrejection', function(e) {
        if (e.reason && e.reason.message && e.reason.message.includes('message channel closed')) {
            e.preventDefault();
            return false;
        }
    });
    
    // Initialize GrapesJS Editor with error handling
    let editor;

    try {
        editor = grapesjs.init({
        container: '#gjs',
        height: '100%',
        width: 'auto',
        
        // Storage configuration
        storageManager: {
            type: 'local',
            autosave: true,
            autoload: true,
            stepsBeforeSave: 1,
            options: {
                local: {
                    key: 'grapesjs-web-builder'
                }
            }
        },
        
        // Layer Manager
        layerManager: {
            appendTo: '#layers-container'
        },
        
        // Style Manager
        styleManager: {
            appendTo: '#style-manager',
            sectors: [
                {
                    name: 'Size',
                    open: false,
                    buildProps: ['width', 'height', 'min-width', 'min-height', 'max-width', 'max-height', 'padding', 'margin']
                },
                {
                    name: 'Space',
                    open: false,
                    buildProps: ['padding-top', 'padding-right', 'padding-bottom', 'padding-left', 'margin-top', 'margin-right', 'margin-bottom', 'margin-left']
                },
                {
                    name: 'Typography',
                    open: false,
                    buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-align', 'text-decoration', 'text-shadow']
                },
                {
                    name: 'Background',
                    open: false,
                    buildProps: ['background-color', 'background-image', 'background-repeat', 'background-position', 'background-attachment', 'background-size']
                },
                {
                    name: 'Borders',
                    open: false,
                    buildProps: ['border-top-width', 'border-right-width', 'border-bottom-width', 'border-left-width', 'border-top-style', 'border-right-style', 'border-bottom-style', 'border-left-style', 'border-top-color', 'border-right-color', 'border-bottom-color', 'border-left-color', 'border-radius']
                },
                {
                    name: 'Effects',
                    open: false,
                    buildProps: ['opacity', 'box-shadow', 'filter']
                }
            ]
        },
        
        // Trait Manager
        traitManager: {
            appendTo: '#trait-manager'
        },
        
        // Device Manager for responsive design
        deviceManager: {
            devices: [
                {
                    name: 'Desktop',
                    width: ''
                },
                {
                    name: 'Tablet',
                    width: '768px',
                    widthMedia: '992px'
                },
                {
                    name: 'Mobile',
                    width: '320px',
                    widthMedia: '768px'
                }
            ]
        },
        
        // Block Manager
        blockManager: {
            appendTo: '#blocks-container'
        },
        
        // Panels configuration
        panels: {
            defaults: []
        },
        
        // Canvas configuration
        canvas: {
            styles: [
                'css/style.css'
            ]
        },

        // From element configuration
        fromElement: true,

        // Avoid component type errors
        avoidInlineStyle: false,
        avoidFrameOffset: true
    });

    } catch (error) {
        console.error('Error initializing GrapesJS:', error);
        document.getElementById('gjs').innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Error loading editor. Please refresh the page.</div>';
        return;
    }

    if (!editor) {
        console.error('Failed to initialize GrapesJS editor');
        return;
    }

    // Make editor globally available for debugging
    window.editor = editor;

    // Add custom commands for better control
    editor.Commands.add('toggle-borders', {
        run: function(editor, sender) {
            const canvas = editor.Canvas.getElement();
            const isVisible = canvas.classList.contains('gjs-dashed');

            if (isVisible) {
                canvas.classList.remove('gjs-dashed');
                sender.set('active', false);
            } else {
                canvas.classList.add('gjs-dashed');
                sender.set('active', true);
            }
        }
    });

    // Add custom command for component visibility
    editor.Commands.add('toggle-component-visibility', {
        run: function(editor, _sender, options = {}) {
            const { component } = options;
            if (!component) return;

            // Use GrapesJS's built-in visibility methods
            const currentStyle = component.get('style') || {};
            const isVisible = currentStyle.display !== 'none';

            if (isVisible) {
                // Hide component
                component.set('style', { ...currentStyle, display: 'none' });
            } else {
                // Show component - remove display none
                const newStyle = { ...currentStyle };
                delete newStyle.display;
                component.set('style', newStyle);
            }

            // Force view refresh
            const view = component.getView();
            if (view) {
                view.render();
            }

            // Trigger updates
            component.trigger('change:style');
            editor.trigger('component:update', component);
            editor.Canvas.getCanvasView().updateFrames();

            return !isVisible; // Return new visibility state
        }
    });

    // Initialize default content structure to match the screenshot exactly
    function initializeDefaultContent() {
        // Wait for editor to be fully loaded
        setTimeout(() => {
            const wrapper = editor.DomComponents.getWrapper();

            // Find and select the heading to show proper hierarchy in layers
            const heading = wrapper.find('.welcome-heading')[0];
            if (heading) {
                // Select the heading to show it in the layers panel
                editor.select(heading);
            }

            // Ensure proper layer names are displayed
            updateLayerNames();
        }, 500);
    }

    // Function to update layer names to match the screenshot
    function updateLayerNames() {
        const wrapper = editor.DomComponents.getWrapper();

        // Find components and set proper names and IDs for the layer manager
        const body = wrapper.find('body')[0];
        if (body) {
            body.set('name', 'Body');
            body.addAttributes({ 'data-layer-id': 'body-layer' });
        }

        const header = wrapper.find('header')[0];
        if (header) {
            header.set('name', 'Header');
            header.addAttributes({ 'data-layer-id': 'header-layer' });
        }

        const contentDiv = wrapper.find('.gjs-content-div')[0];
        if (contentDiv) {
            contentDiv.set('name', 'Div');
            contentDiv.addAttributes({ 'data-layer-id': 'div-layer' });
        }

        const heading = wrapper.find('.welcome-heading')[0];
        if (heading) {
            heading.set('name', 'Heading');
            heading.addAttributes({ 'data-layer-id': 'heading-layer' });
        }

        // Set names for text components
        const textComponents = wrapper.find('p');
        textComponents.forEach((comp, index) => {
            comp.set('name', 'Text');
            comp.addAttributes({ 'data-layer-id': `text-layer-${index}` });
        });
    }

    // Toolbar button handlers
    function initializeToolbarHandlers() {
        // Visibility toggle - properly handle show/hide borders
        document.getElementById('visibility-btn').addEventListener('click', function() {
            const canvas = editor.Canvas.getElement();
            const isVisible = canvas.classList.contains('gjs-dashed');

            if (isVisible) {
                // Hide borders
                canvas.classList.remove('gjs-dashed');
                this.classList.remove('active');
                this.title = 'Show Element Borders';

                // Also remove any outline styles
                const iframeDoc = editor.Canvas.getDocument();
                if (iframeDoc) {
                    const style = iframeDoc.getElementById('gjs-visibility-style');
                    if (style) style.remove();
                }
            } else {
                // Show borders
                canvas.classList.add('gjs-dashed');
                this.classList.add('active');
                this.title = 'Hide Element Borders';

                // Add outline styles to iframe document
                const iframeDoc = editor.Canvas.getDocument();
                if (iframeDoc) {
                    let style = iframeDoc.getElementById('gjs-visibility-style');
                    if (!style) {
                        style = iframeDoc.createElement('style');
                        style.id = 'gjs-visibility-style';
                        style.textContent = `
                            * { outline: 1px dashed rgba(170, 170, 170, 0.7) !important; }
                            *:hover { outline: 2px solid #007bff !important; }
                        `;
                        iframeDoc.head.appendChild(style);
                    }
                }
            }
        });
        
        // Code view
        document.getElementById('code-btn').addEventListener('click', function() {
            editor.runCommand('core:open-code');
        });
        
        // Undo
        document.getElementById('undo-btn').addEventListener('click', function() {
            editor.runCommand('core:undo');
        });
        
        // Redo
        document.getElementById('redo-btn').addEventListener('click', function() {
            editor.runCommand('core:redo');
        });
        
        // Export
        document.getElementById('export-btn').addEventListener('click', function() {
            const html = editor.getHtml();
            const css = editor.getCss();
            const exportContent = `<!DOCTYPE html>
<html>
<head>
    <style>${css}</style>
</head>
<body>
    ${html}
</body>
</html>`;
            
            const blob = new Blob([exportContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'grapesjs-export.html';
            a.click();
            URL.revokeObjectURL(url);
        });
        
        // Settings
        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Settings button clicked');
                showSettingsModal();
            });
            settingsBtn.style.cursor = 'pointer';
        } else {
            console.warn('Settings button not found');
        }

        // Left sidebar toggle
        const leftSidebarToggle = document.getElementById('left-sidebar-toggle');
        if (leftSidebarToggle) {
            leftSidebarToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const leftSidebar = document.querySelector('.sidebar-left');
                if (leftSidebar) {
                    leftSidebar.style.display = leftSidebar.style.display === 'none' ? 'flex' : 'none';
                    this.classList.toggle('active');
                }
            });
            leftSidebarToggle.style.cursor = 'pointer';
        }

        // Right sidebar toggle
        const rightSidebarToggle = document.getElementById('right-sidebar-toggle');
        if (rightSidebarToggle) {
            rightSidebarToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const rightSidebar = document.querySelector('.sidebar-right');
                if (rightSidebar) {
                    rightSidebar.style.display = rightSidebar.style.display === 'none' ? 'flex' : 'none';
                    this.classList.toggle('active');
                }
            });
            rightSidebarToggle.style.cursor = 'pointer';
        }

        // Clear page button
        const clearPageBtn = document.getElementById('clear-page-btn');
        if (clearPageBtn) {
            clearPageBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                if (confirm('Are you sure you want to clear the entire page? This action cannot be undone.')) {
                    editor.setComponents('');
                    editor.setStyle('');
                    console.log('Page cleared');
                }
            });
            clearPageBtn.style.cursor = 'pointer';
        }

        // Import code button
        const importCodeBtn = document.getElementById('import-code-btn');
        if (importCodeBtn) {
            importCodeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                showImportCodeModal();
            });
            importCodeBtn.style.cursor = 'pointer';
        }
        
        // Theme toggle (legacy dark mode)
        document.getElementById('theme-btn').addEventListener('click', function() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);
        });
    }

    // Device selector handler (center toolbar)
    function initializeDeviceSelector() {
        const deviceBtns = document.querySelectorAll('.device-btn');
        const deviceLabel = document.getElementById('device-label');

        deviceBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('Device button clicked:', this.getAttribute('data-device'));

                // Remove active class from all buttons
                deviceBtns.forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');

                // Set device in editor
                const device = this.getAttribute('data-device');
                const deviceName = device.charAt(0).toUpperCase() + device.slice(1);

                // Update label
                if (deviceLabel) {
                    deviceLabel.textContent = deviceName + ' View';
                }

                // Set device in GrapesJS
                try {
                    editor.setDevice(deviceName);
                    console.log('Device set to:', deviceName);
                } catch (error) {
                    console.error('Error setting device:', error);
                }
            });
        });

        // Ensure buttons are properly styled as clickable
        deviceBtns.forEach(btn => {
            btn.style.cursor = 'pointer';
            btn.style.userSelect = 'none';
        });
    }

    // Left sidebar tabs handler
    function initializeLeftSidebarTabs() {
        const tabBtns = document.querySelectorAll('.left-tab-btn');
        const panels = document.querySelectorAll('.content-panel');

        console.log('Initializing left sidebar tabs:', tabBtns.length, 'tabs found');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const panelId = this.getAttribute('data-panel');
                console.log('Tab clicked:', panelId);

                // Remove active class from all tabs and panels
                tabBtns.forEach(b => b.classList.remove('active'));
                panels.forEach(p => p.classList.remove('active'));

                // Add active class to clicked tab and corresponding panel
                this.classList.add('active');
                const targetPanel = document.getElementById(panelId + '-panel');
                if (targetPanel) {
                    targetPanel.classList.add('active');
                    console.log('Panel activated:', panelId + '-panel');
                } else {
                    console.warn('Panel not found:', panelId + '-panel');
                }

                // Initialize content for specific panels
                if (panelId === 'templates') {
                    initializeTemplateLibrary();
                }

                // Initialize content for specific panels
                if (panelId === 'blocks') {
                    initializeBlocks();
                } else if (panelId === 'assets') {
                    initializeAssets();
                }
            });

            // Ensure buttons are properly styled as clickable
            btn.style.cursor = 'pointer';
            btn.style.userSelect = 'none';
        });

        // Templates modal button
        const templatesModalBtn = document.getElementById('open-templates-modal');
        if (templatesModalBtn) {
            templatesModalBtn.addEventListener('click', function(e) {
                e.preventDefault();
                openTemplatesModal();
            });
        }
    }

    // Initialize Assets Panel
    function initializeAssets() {
        const assetsContainer = document.getElementById('assets-container');
        if (!assetsContainer) return;

        assetsContainer.innerHTML = `
            <div style="padding: 20px; text-align: center;">
                <i class="fas fa-images" style="font-size: 48px; color: var(--text-muted); margin-bottom: 16px;"></i>
                <h3 style="color: var(--text-primary); margin-bottom: 8px; font-size: 16px;">Asset Manager</h3>
                <p style="color: var(--text-secondary); font-size: 12px; margin-bottom: 16px;">Upload and manage your images, videos, and files</p>
                <button class="btn-primary" onclick="uploadAsset()">Upload Asset</button>
            </div>
        `;

        // Make uploadAsset globally available
        window.uploadAsset = function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*,video/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    console.log('File selected:', file.name);
                    alert('Asset upload functionality would be implemented here');
                }
            };
            input.click();
        };
    }

    // Templates Modal Function
    function openTemplatesModal() {
        const templates = [
            {
                category: 'Landing Pages',
                items: [
                    {
                        name: 'Hero Landing',
                        description: 'Landing page with hero section',
                        preview: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgZmlsbD0iIzY2N2VlYSIvPjx0ZXh0IHg9IjEwMCIgeT0iNzUiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+SGVybyBMYW5kaW5nPC90ZXh0Pjwvc3ZnPg==',
                        content: `<section style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 80px 20px; text-align: center;"><div style="max-width: 800px; margin: 0 auto;"><h1 style="font-size: 3rem; margin-bottom: 20px; font-weight: bold;">Welcome to Our Platform</h1><p style="font-size: 1.2rem; margin-bottom: 30px; opacity: 0.9;">Build amazing websites with our powerful tools</p><button style="background: #ff6b6b; color: white; padding: 15px 30px; border: none; border-radius: 25px; font-size: 1.1rem; cursor: pointer;">Get Started</button></div></section>`
                    },
                    {
                        name: 'Product Showcase',
                        description: 'Product landing with features',
                        preview: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgZmlsbD0iIzJjM2U1MCIvPjx0ZXh0IHg9IjEwMCIgeT0iNzUiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+UHJvZHVjdDwvdGV4dD48L3N2Zz4=',
                        content: `<header style="background: #2c3e50; color: white; padding: 20px;"><nav style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;"><div style="font-size: 1.5rem; font-weight: bold;">ProductName</div></nav></header><section style="padding: 80px 20px; background: #ecf0f1;"><div style="max-width: 1200px; margin: 0 auto;"><h1 style="font-size: 2.5rem; margin-bottom: 20px; color: #2c3e50;">Revolutionary Product</h1><p style="font-size: 1.1rem; margin-bottom: 30px; color: #7f8c8d;">Transform your workflow</p></div></section>`
                    }
                ]
            },
            {
                category: 'Business',
                items: [
                    {
                        name: 'Corporate',
                        description: 'Professional business layout',
                        preview: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgZmlsbD0iIzM0NDk1ZSIvPjx0ZXh0IHg9IjEwMCIgeT0iNzUiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+Q29ycG9yYXRlPC90ZXh0Pjwvc3ZnPg==',
                        content: `<header style="background: #34495e; color: white; padding: 15px 0;"><div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;"><h1 style="margin: 0; font-size: 1.8rem;">Corporate Solutions</h1></div></header><section style="padding: 60px 20px;"><div style="max-width: 1200px; margin: 0 auto;"><h2 style="text-align: center; margin-bottom: 40px;">Our Services</h2></div></section>`
                    }
                ]
            }
        ];

        let modalContent = `
            <div style="padding: 20px; max-height: 70vh; overflow-y: auto;">
                <h2 style="margin-bottom: 20px; color: var(--text-primary);">Choose a Template</h2>
                <div class="templates-grid">
        `;

        templates.forEach(category => {
            modalContent += `<div class="template-category-section">
                <h3 style="margin: 20px 0 10px 0; color: var(--text-secondary); font-size: 14px;">${category.category}</h3>
                <div class="template-items">`;

            category.items.forEach(template => {
                modalContent += `
                    <div class="template-card" onclick="loadTemplateFromModal('${template.name}', \`${template.content.replace(/`/g, '\\`')}\`)">
                        <div class="template-preview-img">
                            <img src="${template.preview}" alt="${template.name}" style="width: 100%; height: 100px; object-fit: cover; border-radius: 4px;">
                        </div>
                        <div class="template-card-info">
                            <h4>${template.name}</h4>
                            <p>${template.description}</p>
                        </div>
                    </div>
                `;
            });

            modalContent += `</div></div>`;
        });

        modalContent += `
                </div>
            </div>
            <style>
                .templates-grid { display: grid; gap: 20px; }
                .template-items { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; }
                .template-card { border: 1px solid var(--border-color); border-radius: 6px; padding: 12px; cursor: pointer; transition: all 0.2s ease; background: var(--bg-primary); }
                .template-card:hover { border-color: var(--primary-color); box-shadow: var(--shadow-md); }
                .template-card-info h4 { margin: 8px 0 4px 0; font-size: 14px; color: var(--text-primary); }
                .template-card-info p { margin: 0; font-size: 12px; color: var(--text-secondary); }
            </style>
        `;

        editor.Modal.setTitle('Templates')
            .setContent(modalContent)
            .open();

        // Make loadTemplateFromModal globally available
        window.loadTemplateFromModal = function(name, content) {
            if (confirm(`Load "${name}" template? This will replace the current content.`)) {
                editor.setComponents(content);
                editor.setStyle('');
                editor.Modal.close();
                console.log(`Template "${name}" loaded successfully`);
            }
        };
    }

    // Import Code Modal
    function showImportCodeModal() {
        const modalContent = `
            <div style="padding: 20px; max-width: 600px;">
                <h3 style="margin-bottom: 20px; color: var(--text-primary);">Import Code</h3>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary); font-weight: 500;">HTML</label>
                    <textarea id="import-html" placeholder="Paste your HTML code here..." style="width: 100%; height: 200px; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 11px; font-family: monospace; resize: vertical;"></textarea>
                </div>

                <div class="form-group" style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary); font-weight: 500;">CSS</label>
                    <textarea id="import-css" placeholder="Paste your CSS code here..." style="width: 100%; height: 150px; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 11px; font-family: monospace; resize: vertical;"></textarea>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" id="replace-content" checked>
                        <span style="font-size: 12px; color: var(--text-primary);">Replace current content</span>
                    </label>
                    <small style="color: var(--text-muted); font-size: 10px;">Uncheck to append to existing content</small>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button onclick="editor.Modal.close()" style="padding: 8px 16px; border: 1px solid var(--border-color); background: var(--bg-secondary); color: var(--text-primary); border-radius: 4px; cursor: pointer;">Cancel</button>
                    <button onclick="importCode()" style="padding: 8px 16px; border: none; background: var(--primary-color); color: white; border-radius: 4px; cursor: pointer;">Import</button>
                </div>
            </div>
        `;

        editor.Modal.setTitle('Import Code')
            .setContent(modalContent)
            .open();

        // Make import function globally available
        window.importCode = function() {
            const html = document.getElementById('import-html').value;
            const css = document.getElementById('import-css').value;
            const replaceContent = document.getElementById('replace-content').checked;

            try {
                if (replaceContent) {
                    if (html) editor.setComponents(html);
                    if (css) editor.setStyle(css);
                } else {
                    if (html) {
                        const currentHtml = editor.getHtml();
                        editor.setComponents(currentHtml + html);
                    }
                    if (css) {
                        const currentCss = editor.getCss();
                        editor.setStyle(currentCss + '\n' + css);
                    }
                }

                console.log('Code imported successfully');
                alert('Code imported successfully!');
                editor.Modal.close();
            } catch (error) {
                console.error('Error importing code:', error);
                alert('Error importing code. Please check your HTML/CSS syntax.');
            }
        };
    }

    // Project Type Selector
    function initializeProjectTypeSelector() {
        const projectTypeBtns = document.querySelectorAll('.project-type-btn');
        const currentPage = document.getElementById('current-page');

        projectTypeBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const projectType = this.getAttribute('data-type');
                console.log('Project type selected:', projectType);

                // Remove active class from all buttons
                projectTypeBtns.forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');

                // Update current page display
                if (currentPage) {
                    const icon = currentPage.querySelector('i');
                    const text = currentPage.querySelector('span');

                    switch (projectType) {
                        case 'web':
                            icon.className = 'fas fa-globe';
                            text.textContent = 'Web Page';
                            break;
                        case 'email':
                            icon.className = 'fas fa-envelope';
                            text.textContent = 'Email Template';
                            break;
                        case 'document':
                            icon.className = 'fas fa-file-alt';
                            text.textContent = 'Document';
                            break;
                    }
                }

                // Initialize project type specific features
                initializeProjectType(projectType);
            });

            // Ensure buttons are clickable
            btn.style.cursor = 'pointer';
            btn.style.userSelect = 'none';
        });
    }

    // Initialize Project Type Specific Features
    function initializeProjectType(type) {
        console.log('Initializing project type:', type);

        switch (type) {
            case 'email':
                // Email specific initialization
                console.log('Email builder mode activated');
                // Add email-specific blocks and templates
                break;
            case 'document':
                // Document specific initialization
                console.log('Document builder mode activated');
                // Add document-specific blocks and templates
                break;
            case 'web':
            default:
                // Web specific initialization
                console.log('Web builder mode activated');
                // Add web-specific blocks and templates
                break;
        }
    }

    // Pages panel handlers
    function initializePagesPanel() {
        // Add page button
        const addPageBtn = document.getElementById('add-page-btn');
        if (addPageBtn) {
            addPageBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Add page button clicked');
                showAddPageModal();
            });
            // Ensure button is clickable
            addPageBtn.style.cursor = 'pointer';
        } else {
            console.warn('Add page button not found');
        }

        // Page menu button
        const pageMenuBtn = document.getElementById('page-menu-btn');
        if (pageMenuBtn) {
            pageMenuBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Page menu button clicked');
                showPageOptionsModal();
            });
            // Ensure button is clickable
            pageMenuBtn.style.cursor = 'pointer';
        } else {
            console.warn('Page menu button not found');
        }
    }

    // Add Page Modal
    function showAddPageModal() {
        const modalContent = `
            <div style="padding: 20px; max-width: 500px;">
                <h3 style="margin-bottom: 20px; color: var(--text-primary);">Add New Page</h3>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary);">Page Name</label>
                    <input type="text" id="page-name" placeholder="Enter page name" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary);">Page Type</label>
                    <select id="page-type" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                        <option value="web">Web Page</option>
                        <option value="email">Email Template</option>
                        <option value="document">Document</option>
                        <option value="landing">Landing Page</option>
                        <option value="blog">Blog Post</option>
                    </select>
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary);">Template</label>
                    <select id="page-template" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                        <option value="blank">Blank Page</option>
                        <option value="hero">Hero Landing</option>
                        <option value="product">Product Showcase</option>
                        <option value="corporate">Corporate</option>
                        <option value="blog">Blog Layout</option>
                    </select>
                </div>

                <div class="form-group" style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary);">Description</label>
                    <textarea id="page-description" placeholder="Optional page description" style="width: 100%; height: 60px; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px; resize: vertical;"></textarea>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button onclick="editor.Modal.close()" style="padding: 8px 16px; border: 1px solid var(--border-color); background: var(--bg-secondary); color: var(--text-primary); border-radius: 4px; cursor: pointer;">Cancel</button>
                    <button onclick="createNewPage()" style="padding: 8px 16px; border: none; background: var(--primary-color); color: white; border-radius: 4px; cursor: pointer;">Create Page</button>
                </div>
            </div>
        `;

        editor.Modal.setTitle('Add New Page')
            .setContent(modalContent)
            .open();

        // Make createNewPage globally available
        window.createNewPage = function() {
            const name = document.getElementById('page-name').value;
            const type = document.getElementById('page-type').value;
            const template = document.getElementById('page-template').value;
            const description = document.getElementById('page-description').value;

            if (!name.trim()) {
                alert('Please enter a page name');
                return;
            }

            // Create new page logic here
            console.log('Creating new page:', { name, type, template, description });

            // For now, just show success message
            alert(`Page "${name}" created successfully!`);
            editor.Modal.close();
        };
    }

    // Page Options Modal
    function showPageOptionsModal() {
        const modalContent = `
            <div style="padding: 20px; max-width: 400px;">
                <h3 style="margin-bottom: 20px; color: var(--text-primary);">Page Options</h3>

                <div class="page-option-item" onclick="duplicatePage()" style="padding: 12px; border: 1px solid var(--border-color); border-radius: 4px; margin-bottom: 10px; cursor: pointer; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-copy" style="color: var(--primary-color);"></i>
                    <div>
                        <div style="font-weight: 500; font-size: 12px; color: var(--text-primary);">Duplicate Page</div>
                        <div style="font-size: 11px; color: var(--text-secondary);">Create a copy of the current page</div>
                    </div>
                </div>

                <div class="page-option-item" onclick="renamePage()" style="padding: 12px; border: 1px solid var(--border-color); border-radius: 4px; margin-bottom: 10px; cursor: pointer; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-edit" style="color: var(--primary-color);"></i>
                    <div>
                        <div style="font-weight: 500; font-size: 12px; color: var(--text-primary);">Rename Page</div>
                        <div style="font-size: 11px; color: var(--text-secondary);">Change the page name</div>
                    </div>
                </div>

                <div class="page-option-item" onclick="pageSettings()" style="padding: 12px; border: 1px solid var(--border-color); border-radius: 4px; margin-bottom: 10px; cursor: pointer; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-cog" style="color: var(--primary-color);"></i>
                    <div>
                        <div style="font-weight: 500; font-size: 12px; color: var(--text-primary);">Page Settings</div>
                        <div style="font-size: 11px; color: var(--text-secondary);">Configure page properties</div>
                    </div>
                </div>

                <div class="page-option-item" onclick="exportPage()" style="padding: 12px; border: 1px solid var(--border-color); border-radius: 4px; margin-bottom: 10px; cursor: pointer; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-download" style="color: var(--primary-color);"></i>
                    <div>
                        <div style="font-weight: 500; font-size: 12px; color: var(--text-primary);">Export Page</div>
                        <div style="font-size: 11px; color: var(--text-secondary);">Download page as HTML</div>
                    </div>
                </div>

                <div class="page-option-item" onclick="deletePage()" style="padding: 12px; border: 1px solid var(--danger-color); border-radius: 4px; margin-bottom: 10px; cursor: pointer; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-trash" style="color: var(--danger-color);"></i>
                    <div>
                        <div style="font-weight: 500; font-size: 12px; color: var(--danger-color);">Delete Page</div>
                        <div style="font-size: 11px; color: var(--text-secondary);">Permanently remove this page</div>
                    </div>
                </div>
            </div>

            <style>
                .page-option-item:hover {
                    background-color: var(--bg-tertiary);
                }
            </style>
        `;

        editor.Modal.setTitle('Page Options')
            .setContent(modalContent)
            .open();

        // Make page option functions globally available
        window.duplicatePage = function() {
            console.log('Duplicating page...');
            alert('Page duplicated successfully!');
            editor.Modal.close();
        };

        window.renamePage = function() {
            const newName = prompt('Enter new page name:', 'Web');
            if (newName && newName.trim()) {
                console.log('Renaming page to:', newName);
                alert(`Page renamed to "${newName}"!`);
            }
            editor.Modal.close();
        };

        window.pageSettings = function() {
            showPageSettingsModal();
            editor.Modal.close();
        };

        window.exportPage = function() {
            const html = editor.getHtml();
            const css = editor.getCss();
            const exportContent = `<!DOCTYPE html>
<html>
<head>
    <style>${css}</style>
</head>
<body>
    ${html}
</body>
</html>`;

            const blob = new Blob([exportContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'page-export.html';
            a.click();
            URL.revokeObjectURL(url);
            editor.Modal.close();
        };

        window.deletePage = function() {
            if (confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
                console.log('Deleting page...');
                alert('Page deleted successfully!');
            }
            editor.Modal.close();
        };
    }

    // Page Settings Modal
    function showPageSettingsModal() {
        const modalContent = `
            <div style="padding: 20px; max-width: 500px;">
                <h3 style="margin-bottom: 20px; color: var(--text-primary);">Page Settings</h3>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary); font-weight: 500;">Name</label>
                    <input type="text" id="page-name-setting" value="Web" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary); font-weight: 500;">Title</label>
                    <input type="text" id="page-title" value="Web" placeholder="The title is the name of the page that appears in the browser tab." style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                    <small style="color: var(--text-muted); font-size: 10px;">The title is the name of the page that appears in the browser tab.</small>
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary); font-weight: 500;">Description</label>
                    <textarea id="page-description-setting" placeholder="The description is a short summary of the page content." style="width: 100%; height: 60px; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px; resize: vertical;"></textarea>
                    <small style="color: var(--text-muted); font-size: 10px;">The description is a short summary of the page content.</small>
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary); font-weight: 500;">Favicon</label>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="text" id="page-favicon" placeholder="Favicon URL" style="flex: 1; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                        <button onclick="selectFavicon()" style="padding: 8px 12px; border: 1px solid var(--border-color); background: var(--bg-secondary); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 11px;">Select image</button>
                    </div>
                    <small style="color: var(--text-muted); font-size: 10px;">The favicon is the small icon that appears in the browser tab.</small>
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary); font-weight: 500;">Keywords</label>
                    <input type="text" id="page-keywords" placeholder="keyword1, keyword2, keyword3" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                    <small style="color: var(--text-muted); font-size: 10px;">Keywords are words or phrases that describe the content of the page.</small>
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary); font-weight: 500;">Social title</label>
                    <input type="text" id="page-social-title" value="Web" placeholder="Social media title" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                    <small style="color: var(--text-muted); font-size: 10px;">The title is the name of the page that appears on social networks.</small>
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary); font-weight: 500;">Social image</label>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="text" id="page-social-image" placeholder="Social image URL" style="flex: 1; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                        <button onclick="selectSocialImage()" style="padding: 8px 12px; border: 1px solid var(--border-color); background: var(--bg-secondary); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 11px;">Select image</button>
                    </div>
                    <small style="color: var(--text-muted); font-size: 10px;">The image is the picture that appears on social networks.</small>
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary); font-weight: 500;">Social description</label>
                    <textarea id="page-social-description" placeholder="Social media description" style="width: 100%; height: 60px; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px; resize: vertical;"></textarea>
                    <small style="color: var(--text-muted); font-size: 10px;">The description is a short summary of the page content that appears on social networks.</small>
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary); font-weight: 500;">Custom HTML head</label>
                    <textarea id="page-custom-head" placeholder="<meta name='custom' content='value'>" style="width: 100%; height: 80px; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 11px; font-family: monospace; resize: vertical;"></textarea>
                    <small style="color: var(--text-muted); font-size: 10px;">Add any custom HTML (e.g., meta tags, stylesheets, scripts) to be included in the &lt;head&gt; section of the page.</small>
                </div>

                <div class="form-group" style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary); font-weight: 500;">Custom HTML body</label>
                    <textarea id="page-custom-body" placeholder="<script>console.log('Custom script');</script>" style="width: 100%; height: 80px; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 11px; font-family: monospace; resize: vertical;"></textarea>
                    <small style="color: var(--text-muted); font-size: 10px;">Add any custom HTML (e.g., scripts, tracking codes) to be included just before the closing &lt;/body&gt; tag.</small>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button onclick="editor.Modal.close()" style="padding: 8px 16px; border: 1px solid var(--border-color); background: var(--bg-secondary); color: var(--text-primary); border-radius: 4px; cursor: pointer;">Cancel</button>
                    <button onclick="savePageSettings()" style="padding: 8px 16px; border: none; background: var(--primary-color); color: white; border-radius: 4px; cursor: pointer;">Save Settings</button>
                </div>
            </div>
        `;

        editor.Modal.setTitle('Page Settings')
            .setContent(modalContent)
            .open();

        // Make functions globally available
        window.selectFavicon = function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    // In a real implementation, you would upload the file and get a URL
                    document.getElementById('page-favicon').value = 'favicon-' + file.name;
                }
            };
            input.click();
        };

        window.selectSocialImage = function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    // In a real implementation, you would upload the file and get a URL
                    document.getElementById('page-social-image').value = 'social-' + file.name;
                }
            };
            input.click();
        };

        window.savePageSettings = function() {
            const settings = {
                name: document.getElementById('page-name-setting').value,
                title: document.getElementById('page-title').value,
                description: document.getElementById('page-description-setting').value,
                favicon: document.getElementById('page-favicon').value,
                keywords: document.getElementById('page-keywords').value,
                socialTitle: document.getElementById('page-social-title').value,
                socialImage: document.getElementById('page-social-image').value,
                socialDescription: document.getElementById('page-social-description').value,
                customHead: document.getElementById('page-custom-head').value,
                customBody: document.getElementById('page-custom-body').value
            };

            localStorage.setItem('grapesjs-page-settings', JSON.stringify(settings));
            console.log('Page settings saved:', settings);
            alert('Page settings saved successfully!');
            editor.Modal.close();
        };
    }

    // Settings Modal
    function showSettingsModal() {
        const modalContent = `
            <div style="padding: 20px; max-width: 600px;">
                <h3 style="margin-bottom: 20px; color: var(--text-primary);">Settings</h3>

                <!-- General Settings -->
                <div class="settings-section">
                    <h4 style="margin-bottom: 15px; color: var(--text-primary); font-size: 14px; border-bottom: 1px solid var(--border-color); padding-bottom: 5px;">General</h4>

                    <div class="setting-item" style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                            <input type="checkbox" id="autosave" checked style="margin: 0;">
                            <div>
                                <div style="font-size: 12px; color: var(--text-primary);">Auto-save</div>
                                <div style="font-size: 11px; color: var(--text-secondary);">Automatically save changes</div>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item" style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                            <input type="checkbox" id="show-borders" checked style="margin: 0;">
                            <div>
                                <div style="font-size: 12px; color: var(--text-primary);">Show element borders</div>
                                <div style="font-size: 11px; color: var(--text-secondary);">Display borders around elements</div>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item" style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary);">Canvas zoom</label>
                        <select id="canvas-zoom" style="width: 100%; padding: 6px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                            <option value="50">50%</option>
                            <option value="75">75%</option>
                            <option value="100" selected>100%</option>
                            <option value="125">125%</option>
                            <option value="150">150%</option>
                        </select>
                    </div>
                </div>

                <!-- Editor Settings -->
                <div class="settings-section" style="margin-top: 25px;">
                    <h4 style="margin-bottom: 15px; color: var(--text-primary); font-size: 14px; border-bottom: 1px solid var(--border-color); padding-bottom: 5px;">Editor</h4>

                    <div class="setting-item" style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary);">Default font family</label>
                        <select id="default-font" style="width: 100%; padding: 6px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                            <option value="system">System Default</option>
                            <option value="Arial">Arial</option>
                            <option value="Helvetica">Helvetica</option>
                            <option value="Georgia">Georgia</option>
                            <option value="Times New Roman">Times New Roman</option>
                        </select>
                    </div>

                    <div class="setting-item" style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                            <input type="checkbox" id="snap-to-grid" style="margin: 0;">
                            <div>
                                <div style="font-size: 12px; color: var(--text-primary);">Snap to grid</div>
                                <div style="font-size: 11px; color: var(--text-secondary);">Align elements to grid</div>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Export Settings -->
                <div class="settings-section" style="margin-top: 25px;">
                    <h4 style="margin-bottom: 15px; color: var(--text-primary); font-size: 14px; border-bottom: 1px solid var(--border-color); padding-bottom: 5px;">Export</h4>

                    <div class="setting-item" style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                            <input type="checkbox" id="minify-css" checked style="margin: 0;">
                            <div>
                                <div style="font-size: 12px; color: var(--text-primary);">Minify CSS</div>
                                <div style="font-size: 11px; color: var(--text-secondary);">Compress CSS for smaller file size</div>
                            </div>
                        </label>
                    </div>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 25px; padding-top: 15px; border-top: 1px solid var(--border-color);">
                    <button onclick="resetSettings()" style="padding: 8px 16px; border: 1px solid var(--border-color); background: var(--bg-secondary); color: var(--text-primary); border-radius: 4px; cursor: pointer;">Reset to Defaults</button>
                    <button onclick="saveSettings()" style="padding: 8px 16px; border: none; background: var(--primary-color); color: white; border-radius: 4px; cursor: pointer;">Save Settings</button>
                </div>
            </div>
        `;

        editor.Modal.setTitle('Settings')
            .setContent(modalContent)
            .open();

        // Make settings functions globally available
        window.saveSettings = function() {
            const settings = {
                autosave: document.getElementById('autosave').checked,
                showBorders: document.getElementById('show-borders').checked,
                canvasZoom: document.getElementById('canvas-zoom').value,
                defaultFont: document.getElementById('default-font').value,
                snapToGrid: document.getElementById('snap-to-grid').checked,
                minifyCSS: document.getElementById('minify-css').checked
            };

            localStorage.setItem('grapesjs-settings', JSON.stringify(settings));
            console.log('Settings saved:', settings);
            alert('Settings saved successfully!');
            editor.Modal.close();
        };

        window.resetSettings = function() {
            if (confirm('Reset all settings to defaults?')) {
                localStorage.removeItem('grapesjs-settings');
                alert('Settings reset to defaults!');
                editor.Modal.close();
            }
        };
    }

    // Tab switching
    function initializeTabSwitching() {
        const stylesTab = document.getElementById('styles-tab');
        const propertiesTab = document.getElementById('properties-tab');
        const globalStylesTab = document.getElementById('global-styles-tab');
        const styleManager = document.getElementById('style-manager');
        const traitManager = document.getElementById('trait-manager');
        const globalStyles = document.getElementById('global-styles');

        function showTab(activeTab, activePanel) {
            // Remove active class from all tabs
            [stylesTab, propertiesTab, globalStylesTab].forEach(tab => tab.classList.remove('active'));
            // Hide all panels
            [styleManager, traitManager, globalStyles].forEach(panel => panel.style.display = 'none');

            // Show active tab and panel
            activeTab.classList.add('active');
            activePanel.style.display = 'block';
        }

        stylesTab.addEventListener('click', function() {
            showTab(stylesTab, styleManager);
        });

        propertiesTab.addEventListener('click', function() {
            showTab(propertiesTab, traitManager);
        });

        globalStylesTab.addEventListener('click', function() {
            showTab(globalStylesTab, globalStyles);
        });

        // Initially show styles
        showTab(stylesTab, styleManager);
    }

    // Global Styles System
    function initializeGlobalStyles() {
        const globalStylesContainer = document.querySelector('.global-styles-content');

        const globalStylesHTML = `
            <div class="global-styles-section">
                <div class="global-styles-header">
                    <span>Typography</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="global-styles-content">
                    <div class="global-style-group">
                        <label class="global-style-label">Primary Font Family</label>
                        <select class="global-style-input" id="primary-font">
                            <option value="system">System Default</option>
                            <option value="Arial, sans-serif">Arial</option>
                            <option value="Helvetica, sans-serif">Helvetica</option>
                            <option value="Georgia, serif">Georgia</option>
                            <option value="'Times New Roman', serif">Times New Roman</option>
                            <option value="'Courier New', monospace">Courier New</option>
                        </select>
                    </div>
                    <div class="global-style-group">
                        <label class="global-style-label">Base Font Size</label>
                        <div class="global-style-row">
                            <input type="range" class="global-style-input" id="base-font-size" min="12" max="24" value="16">
                            <span id="font-size-value">16px</span>
                        </div>
                    </div>
                    <div class="global-style-group">
                        <label class="global-style-label">Line Height</label>
                        <input type="number" class="global-style-input" id="line-height" value="1.5" step="0.1" min="1" max="3">
                    </div>
                </div>
            </div>

            <div class="global-styles-section">
                <div class="global-styles-header">
                    <span>Colors</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="global-styles-content">
                    <div class="global-style-group">
                        <label class="global-style-label">Primary Color</label>
                        <div class="global-style-row">
                            <input type="color" class="color-picker" id="primary-color" value="#007bff">
                            <input type="text" class="global-style-input" id="primary-color-text" value="#007bff">
                        </div>
                        <div class="global-style-preset">
                            <div class="preset-item" style="background: #007bff;" data-color="#007bff"></div>
                            <div class="preset-item" style="background: #28a745;" data-color="#28a745"></div>
                            <div class="preset-item" style="background: #dc3545;" data-color="#dc3545"></div>
                            <div class="preset-item" style="background: #ffc107;" data-color="#ffc107"></div>
                            <div class="preset-item" style="background: #6f42c1;" data-color="#6f42c1"></div>
                        </div>
                    </div>
                    <div class="global-style-group">
                        <label class="global-style-label">Secondary Color</label>
                        <div class="global-style-row">
                            <input type="color" class="color-picker" id="secondary-color" value="#6c757d">
                            <input type="text" class="global-style-input" id="secondary-color-text" value="#6c757d">
                        </div>
                    </div>
                    <div class="global-style-group">
                        <label class="global-style-label">Text Color</label>
                        <div class="global-style-row">
                            <input type="color" class="color-picker" id="text-color" value="#333333">
                            <input type="text" class="global-style-input" id="text-color-text" value="#333333">
                        </div>
                    </div>
                </div>
            </div>

            <div class="global-styles-section">
                <div class="global-styles-header">
                    <span>Spacing</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="global-styles-content">
                    <div class="global-style-group">
                        <label class="global-style-label">Container Max Width</label>
                        <div class="global-style-row">
                            <input type="range" class="global-style-input" id="container-width" min="800" max="1400" value="1200" step="50">
                            <span id="container-width-value">1200px</span>
                        </div>
                    </div>
                    <div class="global-style-group">
                        <label class="global-style-label">Section Padding</label>
                        <div class="global-style-row">
                            <input type="range" class="global-style-input" id="section-padding" min="10" max="100" value="40" step="5">
                            <span id="section-padding-value">40px</span>
                        </div>
                    </div>
                    <div class="global-style-group">
                        <label class="global-style-label">Element Margin</label>
                        <div class="global-style-row">
                            <input type="range" class="global-style-input" id="element-margin" min="5" max="50" value="15" step="5">
                            <span id="element-margin-value">15px</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="global-style-group" style="margin-top: 20px;">
                <button class="global-style-button" id="apply-global-styles">Apply Global Styles</button>
                <button class="global-style-button secondary" id="reset-global-styles">Reset to Defaults</button>
            </div>
        `;

        globalStylesContainer.innerHTML = globalStylesHTML;

        // Initialize event handlers
        initializeGlobalStylesHandlers();
    }

    function initializeGlobalStylesHandlers() {
        // Font size slider
        const fontSizeSlider = document.getElementById('base-font-size');
        const fontSizeValue = document.getElementById('font-size-value');
        fontSizeSlider.addEventListener('input', function() {
            fontSizeValue.textContent = this.value + 'px';
        });

        // Container width slider
        const containerWidthSlider = document.getElementById('container-width');
        const containerWidthValue = document.getElementById('container-width-value');
        containerWidthSlider.addEventListener('input', function() {
            containerWidthValue.textContent = this.value + 'px';
        });

        // Section padding slider
        const sectionPaddingSlider = document.getElementById('section-padding');
        const sectionPaddingValue = document.getElementById('section-padding-value');
        sectionPaddingSlider.addEventListener('input', function() {
            sectionPaddingValue.textContent = this.value + 'px';
        });

        // Element margin slider
        const elementMarginSlider = document.getElementById('element-margin');
        const elementMarginValue = document.getElementById('element-margin-value');
        elementMarginSlider.addEventListener('input', function() {
            elementMarginValue.textContent = this.value + 'px';
        });

        // Color pickers sync
        ['primary', 'secondary', 'text'].forEach(colorType => {
            const colorPicker = document.getElementById(`${colorType}-color`);
            const colorText = document.getElementById(`${colorType}-color-text`);

            colorPicker.addEventListener('change', function() {
                colorText.value = this.value;
            });

            colorText.addEventListener('change', function() {
                colorPicker.value = this.value;
            });
        });

        // Color presets
        document.querySelectorAll('.preset-item').forEach(preset => {
            preset.addEventListener('click', function() {
                const color = this.getAttribute('data-color');
                document.getElementById('primary-color').value = color;
                document.getElementById('primary-color-text').value = color;
            });
        });

        // Apply global styles
        document.getElementById('apply-global-styles').addEventListener('click', applyGlobalStyles);

        // Reset global styles
        document.getElementById('reset-global-styles').addEventListener('click', resetGlobalStyles);

        // Section collapse/expand
        document.querySelectorAll('.global-styles-header').forEach(header => {
            header.addEventListener('click', function() {
                const content = this.nextElementSibling;
                const icon = this.querySelector('i');

                if (content.style.display === 'none') {
                    content.style.display = 'block';
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    content.style.display = 'none';
                    icon.style.transform = 'rotate(-90deg)';
                }
            });
        });
    }

    function applyGlobalStyles() {
        const styles = {
            fontFamily: document.getElementById('primary-font').value,
            fontSize: document.getElementById('base-font-size').value + 'px',
            lineHeight: document.getElementById('line-height').value,
            primaryColor: document.getElementById('primary-color').value,
            secondaryColor: document.getElementById('secondary-color').value,
            textColor: document.getElementById('text-color').value,
            containerWidth: document.getElementById('container-width').value + 'px',
            sectionPadding: document.getElementById('section-padding').value + 'px',
            elementMargin: document.getElementById('element-margin').value + 'px'
        };

        // Apply styles to the canvas
        const canvas = editor.Canvas.getDocument();
        if (canvas) {
            let globalCSS = `
                body {
                    font-family: ${styles.fontFamily} !important;
                    font-size: ${styles.fontSize} !important;
                    line-height: ${styles.lineHeight} !important;
                    color: ${styles.textColor} !important;
                }
                .container, .gjs-content-div {
                    max-width: ${styles.containerWidth} !important;
                    margin: 0 auto !important;
                }
                section {
                    padding: ${styles.sectionPadding} 20px !important;
                }
                h1, h2, h3, h4, h5, h6, p, div {
                    margin-bottom: ${styles.elementMargin} !important;
                }
                .btn, button {
                    background-color: ${styles.primaryColor} !important;
                }
                a {
                    color: ${styles.primaryColor} !important;
                }
            `;

            // Add or update global styles
            let styleEl = canvas.getElementById('global-styles');
            if (!styleEl) {
                styleEl = canvas.createElement('style');
                styleEl.id = 'global-styles';
                canvas.head.appendChild(styleEl);
            }
            styleEl.textContent = globalCSS;
        }

        // Save global styles
        localStorage.setItem('grapesjs-global-styles', JSON.stringify(styles));

        console.log('Global styles applied:', styles);
    }

    function resetGlobalStyles() {
        if (confirm('Reset all global styles to defaults?')) {
            // Reset form values
            document.getElementById('primary-font').value = 'system';
            document.getElementById('base-font-size').value = '16';
            document.getElementById('font-size-value').textContent = '16px';
            document.getElementById('line-height').value = '1.5';
            document.getElementById('primary-color').value = '#007bff';
            document.getElementById('primary-color-text').value = '#007bff';
            document.getElementById('secondary-color').value = '#6c757d';
            document.getElementById('secondary-color-text').value = '#6c757d';
            document.getElementById('text-color').value = '#333333';
            document.getElementById('text-color-text').value = '#333333';
            document.getElementById('container-width').value = '1200';
            document.getElementById('container-width-value').textContent = '1200px';
            document.getElementById('section-padding').value = '40';
            document.getElementById('section-padding-value').textContent = '40px';
            document.getElementById('element-margin').value = '15';
            document.getElementById('element-margin-value').textContent = '15px';

            // Remove global styles from canvas
            const canvas = editor.Canvas.getDocument();
            if (canvas) {
                const styleEl = canvas.getElementById('global-styles');
                if (styleEl) {
                    styleEl.remove();
                }
            }

            // Clear saved styles
            localStorage.removeItem('grapesjs-global-styles');

            console.log('Global styles reset to defaults');
        }
    }

    // Layout control handlers
    function initializeLayoutControls() {
        const layoutBtns = document.querySelectorAll('.layout-btn');
        layoutBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                layoutBtns.forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');
                
                // Apply text alignment based on button
                const selected = editor.getSelected();
                if (selected) {
                    const icon = this.querySelector('i');
                    if (icon.classList.contains('fa-align-left')) {
                        selected.addStyle({ 'text-align': 'left' });
                    } else if (icon.classList.contains('fa-align-center')) {
                        selected.addStyle({ 'text-align': 'center' });
                    } else if (icon.classList.contains('fa-align-right')) {
                        selected.addStyle({ 'text-align': 'right' });
                    } else if (icon.classList.contains('fa-align-justify')) {
                        selected.addStyle({ 'text-align': 'justify' });
                    }
                }
            });
        });
    }

    // Initialize comprehensive blocks
    function initializeBlocks() {
        const blockManager = editor.BlockManager;

        // Layout Blocks
        blockManager.add('section', {
            label: 'Section',
            category: 'Layout',
            content: `<section style="padding: 20px; margin: 10px 0;">
                <h2>Section Title</h2>
                <p>Section content goes here...</p>
            </section>`,
            attributes: { class: 'gjs-block-section' }
        });

        blockManager.add('container', {
            label: 'Container',
            category: 'Layout',
            content: `<div style="max-width: 1200px; margin: 0 auto; padding: 0 15px;">
                <p>Container content</p>
            </div>`,
            attributes: { class: 'gjs-block-container' }
        });

        blockManager.add('row', {
            label: 'Row',
            category: 'Layout',
            content: `<div style="display: flex; flex-wrap: wrap; margin: 0 -15px;">
                <div style="flex: 1; padding: 0 15px;">Column 1</div>
                <div style="flex: 1; padding: 0 15px;">Column 2</div>
            </div>`,
            attributes: { class: 'gjs-block-row' }
        });

        blockManager.add('column', {
            label: 'Column',
            category: 'Layout',
            content: `<div style="flex: 1; padding: 15px; border: 1px dashed #ccc;">
                Column content
            </div>`,
            attributes: { class: 'gjs-block-column' }
        });

        // Content Blocks
        blockManager.add('text', {
            label: 'Text',
            category: 'Content',
            content: '<div data-gjs-type="text">Insert your text here</div>',
            attributes: { class: 'gjs-block-text' }
        });

        blockManager.add('heading', {
            label: 'Heading',
            category: 'Content',
            content: '<h1>Heading Text</h1>',
            attributes: { class: 'gjs-block-heading' }
        });

        blockManager.add('paragraph', {
            label: 'Paragraph',
            category: 'Content',
            content: '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>',
            attributes: { class: 'gjs-block-paragraph' }
        });

        blockManager.add('list', {
            label: 'List',
            category: 'Content',
            content: `<ul>
                <li>List item 1</li>
                <li>List item 2</li>
                <li>List item 3</li>
            </ul>`,
            attributes: { class: 'gjs-block-list' }
        });

        blockManager.add('quote', {
            label: 'Quote',
            category: 'Content',
            content: `<blockquote style="border-left: 4px solid #007bff; padding-left: 15px; margin: 20px 0; font-style: italic;">
                "This is a quote block"
            </blockquote>`,
            attributes: { class: 'gjs-block-quote' }
        });

        // Media Blocks
        blockManager.add('image', {
            label: 'Image',
            category: 'Media',
            content: { type: 'image' },
            select: true,
            activate: true,
            attributes: { class: 'gjs-block-image' }
        });

        blockManager.add('video', {
            label: 'Video',
            category: 'Media',
            content: `<video controls style="width: 100%; max-width: 100%;">
                <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>`,
            attributes: { class: 'gjs-block-video' }
        });

        blockManager.add('iframe', {
            label: 'Embed',
            category: 'Media',
            content: `<iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                style="width: 100%; height: 315px; border: none;"
                frameborder="0" allowfullscreen></iframe>`,
            attributes: { class: 'gjs-block-iframe' }
        });

        // Form Blocks
        blockManager.add('form', {
            label: 'Form',
            category: 'Forms',
            content: `<form style="padding: 20px; border: 1px solid #ddd; border-radius: 4px;">
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px;">Name:</label>
                    <input type="text" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px;">Email:</label>
                    <input type="email" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                </div>
                <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">Submit</button>
            </form>`,
            attributes: { class: 'gjs-block-form' }
        });

        blockManager.add('input', {
            label: 'Input',
            category: 'Forms',
            content: '<input type="text" placeholder="Enter text..." style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">',
            attributes: { class: 'gjs-block-input' }
        });

        blockManager.add('textarea', {
            label: 'Textarea',
            category: 'Forms',
            content: '<textarea placeholder="Enter your message..." style="width: 100%; height: 100px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; resize: vertical;"></textarea>',
            attributes: { class: 'gjs-block-textarea' }
        });

        blockManager.add('button', {
            label: 'Button',
            category: 'Forms',
            content: '<button style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">Click Me</button>',
            attributes: { class: 'gjs-block-button' }
        });

        // Navigation Blocks
        blockManager.add('navbar', {
            label: 'Navbar',
            category: 'Navigation',
            content: `<nav style="background: #333; padding: 10px 0;">
                <div style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center; padding: 0 15px;">
                    <div style="color: white; font-weight: bold;">Brand</div>
                    <div>
                        <a href="#" style="color: white; text-decoration: none; margin: 0 15px;">Home</a>
                        <a href="#" style="color: white; text-decoration: none; margin: 0 15px;">About</a>
                        <a href="#" style="color: white; text-decoration: none; margin: 0 15px;">Contact</a>
                    </div>
                </div>
            </nav>`,
            attributes: { class: 'gjs-block-navbar' }
        });

        blockManager.add('breadcrumb', {
            label: 'Breadcrumb',
            category: 'Navigation',
            content: `<nav style="padding: 10px 0;">
                <a href="#" style="color: #007bff; text-decoration: none;">Home</a>
                <span style="margin: 0 5px;">/</span>
                <a href="#" style="color: #007bff; text-decoration: none;">Category</a>
                <span style="margin: 0 5px;">/</span>
                <span style="color: #666;">Current Page</span>
            </nav>`,
            attributes: { class: 'gjs-block-breadcrumb' }
        });
    }

    // Theme Management System
    function initializeThemeSystem() {
        const themeDropdown = document.getElementById('theme-dropdown');
        const themeOptions = document.getElementById('theme-options');
        const currentThemeSpan = document.getElementById('current-theme');

        // Load saved theme
        const savedTheme = localStorage.getItem('grapesjs-theme') || 'light';
        setTheme(savedTheme);

        // Theme dropdown toggle
        themeDropdown.addEventListener('click', function(e) {
            e.stopPropagation();
            themeOptions.classList.toggle('show');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function() {
            themeOptions.classList.remove('show');
        });

        // Theme option selection
        themeOptions.addEventListener('click', function(e) {
            if (e.target.classList.contains('theme-option')) {
                const theme = e.target.getAttribute('data-theme');
                setTheme(theme);
                themeOptions.classList.remove('show');
            }
        });
    }

    function setTheme(theme) {
        // Update document theme
        document.documentElement.setAttribute('data-theme', theme);

        // Update current theme display
        const currentThemeSpan = document.getElementById('current-theme');
        if (currentThemeSpan) {
            currentThemeSpan.textContent = theme.charAt(0).toUpperCase() + theme.slice(1);
        }

        // Update active theme option
        document.querySelectorAll('.theme-option').forEach(option => {
            option.classList.remove('active');
            if (option.getAttribute('data-theme') === theme) {
                option.classList.add('active');
            }
        });

        // Update theme toggle button icon
        const themeBtn = document.getElementById('theme-btn');
        const icon = themeBtn?.querySelector('i');
        if (icon) {
            icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        // Save theme preference
        localStorage.setItem('grapesjs-theme', theme);

        // DO NOT apply theme to canvas - keep canvas content unaffected by dark mode
        // The canvas should always maintain the original styling of the web page being built

        console.log(`Theme changed to: ${theme}`);
    }

    // Template Library System
    function initializeTemplateLibrary() {
        const templatesContainer = document.getElementById('templates-container');

        // Check if element exists before proceeding
        if (!templatesContainer) {
            console.warn('Templates container not found, skipping template initialization');
            return;
        }

        const templates = [
            {
                category: 'Landing Pages',
                items: [
                    {
                        name: 'Hero Landing',
                        description: 'Landing page with hero section',
                        icon: '🚀',
                        content: `
                            <section style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 80px 20px; text-align: center;">
                                <div style="max-width: 800px; margin: 0 auto;">
                                    <h1 style="font-size: 3rem; margin-bottom: 20px; font-weight: bold;">Welcome to Our Platform</h1>
                                    <p style="font-size: 1.2rem; margin-bottom: 30px; opacity: 0.9;">Build amazing websites with our powerful tools and features</p>
                                    <button style="background: #ff6b6b; color: white; padding: 15px 30px; border: none; border-radius: 25px; font-size: 1.1rem; cursor: pointer;">Get Started</button>
                                </div>
                            </section>
                            <section style="padding: 60px 20px;">
                                <div style="max-width: 1200px; margin: 0 auto; display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                                    <div style="text-align: center; padding: 20px;">
                                        <h3>Feature One</h3>
                                        <p>Description of your amazing feature</p>
                                    </div>
                                    <div style="text-align: center; padding: 20px;">
                                        <h3>Feature Two</h3>
                                        <p>Description of your amazing feature</p>
                                    </div>
                                    <div style="text-align: center; padding: 20px;">
                                        <h3>Feature Three</h3>
                                        <p>Description of your amazing feature</p>
                                    </div>
                                </div>
                            </section>`
                    },
                    {
                        name: 'Product Showcase',
                        description: 'Product landing with features',
                        icon: '📱',
                        content: `
                            <header style="background: #2c3e50; color: white; padding: 20px;">
                                <nav style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                                    <div style="font-size: 1.5rem; font-weight: bold;">ProductName</div>
                                    <div>
                                        <a href="#" style="color: white; text-decoration: none; margin: 0 15px;">Features</a>
                                        <a href="#" style="color: white; text-decoration: none; margin: 0 15px;">Pricing</a>
                                        <a href="#" style="color: white; text-decoration: none; margin: 0 15px;">Contact</a>
                                    </div>
                                </nav>
                            </header>
                            <section style="padding: 80px 20px; background: #ecf0f1;">
                                <div style="max-width: 1200px; margin: 0 auto; display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                                    <div>
                                        <h1 style="font-size: 2.5rem; margin-bottom: 20px; color: #2c3e50;">Revolutionary Product</h1>
                                        <p style="font-size: 1.1rem; margin-bottom: 30px; color: #7f8c8d;">Transform your workflow with our innovative solution</p>
                                        <button style="background: #3498db; color: white; padding: 12px 24px; border: none; border-radius: 5px; font-size: 1rem;">Learn More</button>
                                    </div>
                                    <div style="background: #bdc3c7; height: 300px; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #7f8c8d;">
                                        Product Image
                                    </div>
                                </div>
                            </section>`
                    }
                ]
            },
            {
                category: 'Business',
                items: [
                    {
                        name: 'Corporate',
                        description: 'Professional business layout',
                        icon: '🏢',
                        content: `
                            <header style="background: #34495e; color: white; padding: 15px 0;">
                                <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
                                    <h1 style="margin: 0; font-size: 1.8rem;">Corporate Solutions</h1>
                                </div>
                            </header>
                            <section style="padding: 60px 20px;">
                                <div style="max-width: 1200px; margin: 0 auto;">
                                    <h2 style="text-align: center; margin-bottom: 40px; color: #2c3e50;">Our Services</h2>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px;">
                                        <div style="padding: 30px; border: 1px solid #bdc3c7; border-radius: 8px; text-align: center;">
                                            <h3 style="color: #34495e;">Consulting</h3>
                                            <p>Expert business consulting services</p>
                                        </div>
                                        <div style="padding: 30px; border: 1px solid #bdc3c7; border-radius: 8px; text-align: center;">
                                            <h3 style="color: #34495e;">Development</h3>
                                            <p>Custom software development</p>
                                        </div>
                                        <div style="padding: 30px; border: 1px solid #bdc3c7; border-radius: 8px; text-align: center;">
                                            <h3 style="color: #34495e;">Support</h3>
                                            <p>24/7 technical support</p>
                                        </div>
                                    </div>
                                </div>
                            </section>`
                    },
                    {
                        name: 'Agency Portfolio',
                        description: 'Creative agency showcase',
                        icon: '🎨',
                        content: `
                            <section style="background: #1a1a1a; color: white; padding: 100px 20px; text-align: center;">
                                <h1 style="font-size: 4rem; margin-bottom: 20px; font-weight: 300;">Creative Agency</h1>
                                <p style="font-size: 1.3rem; opacity: 0.8;">We create beautiful digital experiences</p>
                            </section>
                            <section style="padding: 80px 20px;">
                                <div style="max-width: 1200px; margin: 0 auto;">
                                    <h2 style="text-align: center; margin-bottom: 50px; font-size: 2.5rem; color: #333;">Our Work</h2>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                                        <div style="background: #f8f9fa; height: 250px; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #6c757d;">Project 1</div>
                                        <div style="background: #f8f9fa; height: 250px; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #6c757d;">Project 2</div>
                                        <div style="background: #f8f9fa; height: 250px; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #6c757d;">Project 3</div>
                                    </div>
                                </div>
                            </section>`
                    }
                ]
            },
            {
                category: 'Blog & Content',
                items: [
                    {
                        name: 'Blog Layout',
                        description: 'Clean blog template',
                        icon: '📝',
                        content: `
                            <header style="background: #fff; border-bottom: 1px solid #e9ecef; padding: 20px 0;">
                                <div style="max-width: 800px; margin: 0 auto; padding: 0 20px;">
                                    <h1 style="margin: 0; color: #343a40;">My Blog</h1>
                                    <p style="margin: 5px 0 0 0; color: #6c757d;">Thoughts and ideas</p>
                                </div>
                            </header>
                            <main style="max-width: 800px; margin: 40px auto; padding: 0 20px;">
                                <article style="margin-bottom: 40px; padding-bottom: 40px; border-bottom: 1px solid #e9ecef;">
                                    <h2 style="color: #343a40; margin-bottom: 10px;">Blog Post Title</h2>
                                    <p style="color: #6c757d; font-size: 0.9rem; margin-bottom: 20px;">Published on March 15, 2024</p>
                                    <p style="line-height: 1.6; color: #495057;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                                    <a href="#" style="color: #007bff; text-decoration: none;">Read more →</a>
                                </article>
                                <article style="margin-bottom: 40px;">
                                    <h2 style="color: #343a40; margin-bottom: 10px;">Another Blog Post</h2>
                                    <p style="color: #6c757d; font-size: 0.9rem; margin-bottom: 20px;">Published on March 10, 2024</p>
                                    <p style="line-height: 1.6; color: #495057;">Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                                    <a href="#" style="color: #007bff; text-decoration: none;">Read more →</a>
                                </article>
                            </main>`
                    }
                ]
            }
        ];

        // Render templates in sidebar format
        let html = '';
        templates.forEach(category => {
            html += `<div class="template-category">${category.category}</div>`;
            category.items.forEach(template => {
                html += `
                    <div class="template-item" data-template="${template.name}" onclick="loadTemplate('${template.name}')">
                        <div class="template-preview">${template.icon}</div>
                        <div class="template-info">
                            <div class="template-name">${template.name}</div>
                            <div class="template-description">${template.description}</div>
                        </div>
                    </div>
                `;
            });
        });

        templatesContainer.innerHTML = html;

        // Make loadTemplate globally available for onclick handlers
        window.loadTemplate = function(templateName) {
            const template = templates
                .flatMap(cat => cat.items)
                .find(t => t.name === templateName);

            if (template) {
                if (confirm(`Load "${template.name}" template? This will replace the current content.`)) {
                    editor.setComponents(template.content);
                    editor.setStyle('');
                    console.log(`Template "${template.name}" loaded successfully`);
                }
            }
        };

        // Template menu handler
        document.getElementById('template-menu-btn').addEventListener('click', function() {
            editor.Modal.setTitle('Template Options')
                .setContent(`
                    <div style="padding: 20px;">
                        <h4>Template Actions</h4>
                        <button onclick="saveCurrentAsTemplate()" style="margin: 5px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px;">Save Current as Template</button>
                        <button onclick="importTemplate()" style="margin: 5px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px;">Import Template</button>
                        <button onclick="exportTemplates()" style="margin: 5px; padding: 8px 16px; background: #17a2b8; color: white; border: none; border-radius: 4px;">Export Templates</button>
                    </div>
                `)
                .open();
        });
    }



    // Context Menu System
    function initializeContextMenu() {
        const contextMenu = document.getElementById('context-menu');
        let currentComponent = null;

        // Show context menu on right click in canvas
        editor.on('canvas:contextmenu', function(e) {
            e.preventDefault();
            const component = editor.getSelected();
            if (component) {
                currentComponent = component;
                showContextMenu(e.clientX, e.clientY);
            }
        });

        // Hide context menu on click outside
        document.addEventListener('click', function() {
            hideContextMenu();
        });

        // Handle context menu actions
        contextMenu.addEventListener('click', function(e) {
            const action = e.target.closest('.context-menu-item')?.getAttribute('data-action');
            if (action && currentComponent) {
                handleContextAction(action, currentComponent);
            }
            hideContextMenu();
        });

        function showContextMenu(x, y) {
            contextMenu.style.display = 'block';
            contextMenu.style.left = x + 'px';
            contextMenu.style.top = y + 'px';

            // Adjust position if menu goes off screen
            const rect = contextMenu.getBoundingClientRect();
            if (rect.right > window.innerWidth) {
                contextMenu.style.left = (x - rect.width) + 'px';
            }
            if (rect.bottom > window.innerHeight) {
                contextMenu.style.top = (y - rect.height) + 'px';
            }
        }

        function hideContextMenu() {
            contextMenu.style.display = 'none';
        }

        function handleContextAction(action, component) {
            switch (action) {
                case 'copy':
                    editor.runCommand('core:copy', { component });
                    break;
                case 'duplicate':
                    const parent = component.parent();
                    if (parent) {
                        const clone = component.clone();
                        parent.append(clone);
                        editor.select(clone);
                    }
                    break;
                case 'delete':
                    if (confirm('Delete this component?')) {
                        component.remove();
                    }
                    break;
                case 'inspect':
                    editor.Modal.setTitle('Component Inspector')
                        .setContent(`
                            <div style="padding: 20px; font-family: monospace; font-size: 12px;">
                                <h4>Component Details:</h4>
                                <p><strong>Type:</strong> ${component.get('tagName')}</p>
                                <p><strong>Classes:</strong> ${component.getClasses().join(', ') || 'None'}</p>
                                <p><strong>ID:</strong> ${component.getId() || 'None'}</p>
                                <p><strong>Styles:</strong> ${JSON.stringify(component.getStyle(), null, 2)}</p>
                                <p><strong>Content:</strong> ${component.get('content') || 'None'}</p>
                                <p><strong>Children:</strong> ${component.components().length}</p>
                            </div>
                        `)
                        .open();
                    break;
            }
        }
    }

    // Initialize all components
    editor.on('load', function() {
        initializeBlocks();
        initializeThemeSystem();
        initializeTemplateLibrary();
        initializeGlobalStyles();
        initializeContextMenu();
        initializeProjectTypeSelector();
        initializeDefaultContent();
        initializeToolbarHandlers();
        initializeDeviceSelector();
        initializeLeftSidebarTabs();
        initializePagesPanel();
        initializeTabSwitching();
        initializeLayoutControls();
        initializeSectionCollapse();
        initializeLayerVisibility();

        // Add simple event delegation for layer visibility with component tracking
        const componentVisibilityMap = new Map();

        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('gjs-lm-vis')) {
                e.stopPropagation();
                e.preventDefault();

                const layerEl = e.target.closest('.gjs-lm-layer');
                if (layerEl) {
                    const layerName = layerEl.querySelector('.gjs-lm-layer-name')?.textContent?.trim();
                    console.log('Clicked visibility for layer:', layerName);

                    // Get or find component
                    let component = componentVisibilityMap.get(layerName);

                    if (!component) {
                        // Find and cache the component
                        const wrapper = editor.DomComponents.getWrapper();

                        switch(layerName?.toLowerCase()) {
                            case 'body':
                                component = wrapper.find('body')[0] || wrapper.find('.gjs-body')[0];
                                break;
                            case 'header':
                                component = wrapper.find('header')[0] || wrapper.find('.gjs-header')[0];
                                break;
                            case 'div':
                                component = wrapper.find('.gjs-content-div')[0];
                                break;
                            case 'heading':
                                component = wrapper.find('.welcome-heading')[0] || wrapper.find('h2')[0];
                                break;
                            case 'text':
                                // For text, we need to handle multiple text elements
                                const paragraphs = wrapper.find('p');
                                if (paragraphs.length > 0) {
                                    // Find which text element this layer represents
                                    const textLayers = Array.from(layerEl.parentElement.children).filter(el =>
                                        el.querySelector('.gjs-lm-layer-name')?.textContent?.trim().toLowerCase() === 'text'
                                    );
                                    const textIndex = textLayers.indexOf(layerEl);
                                    component = paragraphs[textIndex] || paragraphs[0];
                                }
                                break;
                        }

                        if (component) {
                            componentVisibilityMap.set(layerName, component);
                        }
                    }

                    if (component) {
                        // Use the custom command for reliable visibility toggle
                        const newVisibilityState = editor.runCommand('toggle-component-visibility', { component });

                        // Update layer appearance
                        if (newVisibilityState) {
                            // Component is now visible
                            layerEl.classList.remove('gjs-lm-hide');
                            e.target.title = 'Hide element';
                            console.log(`${layerName} is now visible`);
                        } else {
                            // Component is now hidden
                            layerEl.classList.add('gjs-lm-hide');
                            e.target.title = 'Show element';
                            console.log(`${layerName} is now hidden`);
                        }

                        // Debug logging
                        setTimeout(() => {
                            const finalStyles = component.get('style') || {};
                            console.log(`Final styles for ${layerName}:`, finalStyles);
                        }, 100);

                    } else {
                        console.error(`Could not find component for layer: ${layerName}`);
                    }
                }
            }
        });

        // Set initial visibility state
        const visibilityBtn = document.getElementById('visibility-btn');
        const canvas = editor.Canvas.getElement();
        canvas.classList.add('gjs-dashed');
        visibilityBtn.classList.add('active');
        visibilityBtn.title = 'Hide Element Borders';

        console.log('GrapesJS Web Builder initialized successfully!');
    });

    // Update selection info when component is selected
    editor.on('component:selected', function(component) {
        const selectionInfo = document.querySelector('.selection-item span:first-of-type');
        const selectionValue = document.querySelector('.selection-value');

        if (component) {
            const tagName = component.get('tagName');
            const className = component.getClasses().join(' ');
            const componentName = component.get('name') || tagName;

            selectionInfo.textContent = componentName.charAt(0).toUpperCase() + componentName.slice(1);
            selectionValue.textContent = className || component.get('type') || 'title';

            // Update layout button states based on component styles
            updateLayoutButtonStates(component);
        }
    });

    // Function to update layout button states
    function updateLayoutButtonStates(component) {
        const layoutBtns = document.querySelectorAll('.layout-btn');
        const textAlign = component.getStyle()['text-align'] || 'left';

        layoutBtns.forEach(btn => btn.classList.remove('active'));

        const alignmentMap = {
            'left': 'fa-align-left',
            'center': 'fa-align-center',
            'right': 'fa-align-right',
            'justify': 'fa-align-justify'
        };

        const targetIcon = alignmentMap[textAlign];
        if (targetIcon) {
            const targetBtn = document.querySelector(`.layout-btn i.${targetIcon}`)?.parentElement;
            if (targetBtn) {
                targetBtn.classList.add('active');
            }
        }
    }

    // Add section collapse/expand functionality
    function initializeSectionCollapse() {
        // Handle style manager section collapse
        editor.on('styleManager:sector:toggle', function(sector) {
            const sectorEl = sector.getEl();
            if (sectorEl) {
                sectorEl.classList.toggle('gjs-sm-open');
            }
        });

        // Handle custom property section collapse
        document.addEventListener('click', function(e) {
            if (e.target.closest('.section-header')) {
                const header = e.target.closest('.section-header');
                const section = header.parentElement;
                const content = section.querySelector('.layout-controls');

                if (content) {
                    const isCollapsed = content.style.display === 'none';
                    content.style.display = isCollapsed ? 'flex' : 'none';
                    header.classList.toggle('collapsed', !isCollapsed);
                }
            }
        });
    }

    // Initialize layer visibility functionality
    function initializeLayerVisibility() {
        // Use MutationObserver to watch for layer changes
        const layersContainer = document.getElementById('layers-container');

        if (layersContainer) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                addVisibilityToLayers(node);
                            }
                        });
                    }
                });
            });

            observer.observe(layersContainer, {
                childList: true,
                subtree: true
            });

            // Add visibility to existing layers
            setTimeout(() => {
                addVisibilityToLayers(layersContainer);
            }, 1500);
        }

        function addVisibilityToLayers(container) {
            const layers = container.querySelectorAll('.gjs-lm-layer');

            layers.forEach(layerEl => {
                if (!layerEl.querySelector('.gjs-lm-vis')) {
                    const titleEl = layerEl.querySelector('.gjs-lm-title');
                    if (titleEl) {
                        const visToggle = document.createElement('div');
                        visToggle.className = 'gjs-lm-vis';
                        visToggle.title = 'Toggle visibility';

                        titleEl.appendChild(visToggle);

                        // Add click handler
                        visToggle.addEventListener('click', function(e) {
                            e.stopPropagation();
                            e.preventDefault();

                            // Find the component associated with this layer
                            const layerName = layerEl.querySelector('.gjs-lm-layer-name')?.textContent;
                            const wrapper = editor.DomComponents.getWrapper();

                            // Try to find component by various methods
                            let component = null;

                            // Method 1: Find by layer element data
                            const layerId = layerEl.getAttribute('data-layer-id');
                            if (layerId) {
                                component = wrapper.find(`#${layerId}`)[0];
                            }

                            // Method 2: Find by tag name or class
                            if (!component && layerName) {
                                if (layerName.toLowerCase() === 'body') {
                                    component = wrapper.find('body')[0] || wrapper.find('.gjs-body')[0];
                                } else if (layerName.toLowerCase() === 'header') {
                                    component = wrapper.find('header')[0] || wrapper.find('.gjs-header')[0];
                                } else if (layerName.toLowerCase() === 'div') {
                                    component = wrapper.find('.gjs-content-div')[0];
                                } else if (layerName.toLowerCase() === 'heading') {
                                    component = wrapper.find('.welcome-heading')[0] || wrapper.find('h2')[0];
                                } else if (layerName.toLowerCase() === 'text') {
                                    // For text elements, find the first text component that's not hidden
                                    const textComponents = wrapper.find('p');
                                    component = textComponents.find(comp => comp.getStyle().display !== 'none') || textComponents[0];
                                }
                            }

                            if (component) {
                                const currentDisplay = component.getStyle().display;
                                const isHidden = currentDisplay === 'none';

                                if (isHidden) {
                                    // Show the component
                                    component.removeStyle('display');
                                    layerEl.classList.remove('gjs-lm-hide');
                                    visToggle.title = 'Hide element';
                                } else {
                                    // Hide the component
                                    component.addStyle({ display: 'none' });
                                    layerEl.classList.add('gjs-lm-hide');
                                    visToggle.title = 'Show element';
                                }

                                // Force canvas refresh
                                editor.refresh();
                                console.log(`Toggled visibility for ${layerName}:`, !isHidden ? 'hidden' : 'visible');
                            } else {
                                console.warn(`Could not find component for layer: ${layerName}`);
                            }
                        });
                    }
                }
            });
        }
    }
});
