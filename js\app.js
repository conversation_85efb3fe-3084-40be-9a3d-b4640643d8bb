// GrapesJS Web Builder Application
document.addEventListener('DOMContentLoaded', function() {

    // Prevent extension-related errors from affecting the app
    window.addEventListener('error', function(e) {
        if (e.message && e.message.includes('message channel closed')) {
            e.preventDefault();
            return false;
        }
    });

    window.addEventListener('unhandledrejection', function(e) {
        if (e.reason && e.reason.message && e.reason.message.includes('message channel closed')) {
            e.preventDefault();
            return false;
        }
    });
    
    // Initialize GrapesJS Editor with error handling
    let editor;

    try {
        editor = grapesjs.init({
        container: '#gjs',
        height: '100%',
        width: 'auto',
        
        // Storage configuration
        storageManager: {
            type: 'local',
            autosave: true,
            autoload: true,
            stepsBeforeSave: 1,
            options: {
                local: {
                    key: 'grapesjs-web-builder'
                }
            }
        },
        
        // Layer Manager
        layerManager: {
            appendTo: '#layers-container'
        },
        
        // Style Manager
        styleManager: {
            appendTo: '#style-manager',
            sectors: [
                {
                    name: 'Size',
                    open: false,
                    buildProps: ['width', 'height', 'min-width', 'min-height', 'max-width', 'max-height', 'padding', 'margin']
                },
                {
                    name: 'Space',
                    open: false,
                    buildProps: ['padding-top', 'padding-right', 'padding-bottom', 'padding-left', 'margin-top', 'margin-right', 'margin-bottom', 'margin-left']
                },
                {
                    name: 'Typography',
                    open: false,
                    buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-align', 'text-decoration', 'text-shadow']
                },
                {
                    name: 'Background',
                    open: false,
                    buildProps: ['background-color', 'background-image', 'background-repeat', 'background-position', 'background-attachment', 'background-size']
                },
                {
                    name: 'Borders',
                    open: false,
                    buildProps: ['border-top-width', 'border-right-width', 'border-bottom-width', 'border-left-width', 'border-top-style', 'border-right-style', 'border-bottom-style', 'border-left-style', 'border-top-color', 'border-right-color', 'border-bottom-color', 'border-left-color', 'border-radius']
                },
                {
                    name: 'Effects',
                    open: false,
                    buildProps: ['opacity', 'box-shadow', 'filter']
                }
            ]
        },
        
        // Trait Manager
        traitManager: {
            appendTo: '#trait-manager'
        },
        
        // Device Manager for responsive design
        deviceManager: {
            devices: [
                {
                    name: 'Desktop',
                    width: ''
                },
                {
                    name: 'Tablet',
                    width: '768px',
                    widthMedia: '992px'
                },
                {
                    name: 'Mobile',
                    width: '320px',
                    widthMedia: '768px'
                }
            ]
        },
        
        // Block Manager
        blockManager: {
            appendTo: null // We'll handle blocks separately
        },
        
        // Panels configuration
        panels: {
            defaults: []
        },
        
        // Canvas configuration
        canvas: {
            styles: [
                'css/style.css'
            ]
        },
        
        // From element configuration
        fromElement: true
    });

    } catch (error) {
        console.error('Error initializing GrapesJS:', error);
        document.getElementById('gjs').innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Error loading editor. Please refresh the page.</div>';
        return;
    }

    if (!editor) {
        console.error('Failed to initialize GrapesJS editor');
        return;
    }

    // Make editor globally available for debugging
    window.editor = editor;

    // Add custom commands for better control
    editor.Commands.add('toggle-borders', {
        run: function(editor, sender) {
            const canvas = editor.Canvas.getElement();
            const isVisible = canvas.classList.contains('gjs-dashed');

            if (isVisible) {
                canvas.classList.remove('gjs-dashed');
                sender.set('active', false);
            } else {
                canvas.classList.add('gjs-dashed');
                sender.set('active', true);
            }
        }
    });

    // Initialize default content structure to match the screenshot exactly
    function initializeDefaultContent() {
        // Wait for editor to be fully loaded
        setTimeout(() => {
            const wrapper = editor.DomComponents.getWrapper();

            // Find and select the heading to show proper hierarchy in layers
            const heading = wrapper.find('.welcome-heading')[0];
            if (heading) {
                // Add the title trait to the heading
                heading.addTrait({
                    type: 'text',
                    name: 'title',
                    label: 'Title',
                    value: 'title'
                });

                // Select the heading to show it in the layers panel
                editor.select(heading);

                // Ensure the heading has the proper structure for the layers panel
                // Add text components if they don't exist
                if (heading.components().length === 0) {
                    heading.components([
                        {
                            tagName: 'span',
                            type: 'text',
                            content: 'Text 1',
                            attributes: { class: 'text-component' }
                        },
                        {
                            tagName: 'span',
                            type: 'text',
                            content: 'Text 2',
                            attributes: { class: 'text-component' }
                        },
                        {
                            tagName: 'span',
                            type: 'text',
                            content: 'Text 3',
                            attributes: { class: 'text-component' }
                        }
                    ]);
                }
            }

            // Ensure proper layer names are displayed
            updateLayerNames();
        }, 500);
    }

    // Function to update layer names to match the screenshot
    function updateLayerNames() {
        const wrapper = editor.DomComponents.getWrapper();

        // Find components and set proper names for the layer manager
        const body = wrapper.find('body')[0];
        if (body) {
            body.set('name', 'Body');
        }

        const header = wrapper.find('header')[0];
        if (header) {
            header.set('name', 'Header');
        }

        const contentDiv = wrapper.find('.gjs-content-div')[0];
        if (contentDiv) {
            contentDiv.set('name', 'Div');
        }

        const heading = wrapper.find('.welcome-heading')[0];
        if (heading) {
            heading.set('name', 'Heading');
        }

        // Set names for text components
        const textComponents = wrapper.find('.text-component');
        textComponents.forEach((comp) => {
            comp.set('name', 'Text');
        });
    }

    // Toolbar button handlers
    function initializeToolbarHandlers() {
        // Visibility toggle - properly handle show/hide borders
        document.getElementById('visibility-btn').addEventListener('click', function() {
            const canvas = editor.Canvas.getElement();
            const isVisible = canvas.classList.contains('gjs-dashed');

            if (isVisible) {
                // Hide borders
                canvas.classList.remove('gjs-dashed');
                this.classList.remove('active');
                this.title = 'Show Element Borders';

                // Also remove any outline styles
                const iframeDoc = editor.Canvas.getDocument();
                if (iframeDoc) {
                    const style = iframeDoc.getElementById('gjs-visibility-style');
                    if (style) style.remove();
                }
            } else {
                // Show borders
                canvas.classList.add('gjs-dashed');
                this.classList.add('active');
                this.title = 'Hide Element Borders';

                // Add outline styles to iframe document
                const iframeDoc = editor.Canvas.getDocument();
                if (iframeDoc) {
                    let style = iframeDoc.getElementById('gjs-visibility-style');
                    if (!style) {
                        style = iframeDoc.createElement('style');
                        style.id = 'gjs-visibility-style';
                        style.textContent = `
                            * { outline: 1px dashed rgba(170, 170, 170, 0.7) !important; }
                            *:hover { outline: 2px solid #007bff !important; }
                        `;
                        iframeDoc.head.appendChild(style);
                    }
                }
            }
        });
        
        // Code view
        document.getElementById('code-btn').addEventListener('click', function() {
            editor.runCommand('core:open-code');
        });
        
        // Undo
        document.getElementById('undo-btn').addEventListener('click', function() {
            editor.runCommand('core:undo');
        });
        
        // Redo
        document.getElementById('redo-btn').addEventListener('click', function() {
            editor.runCommand('core:redo');
        });
        
        // Export
        document.getElementById('export-btn').addEventListener('click', function() {
            const html = editor.getHtml();
            const css = editor.getCss();
            const exportContent = `<!DOCTYPE html>
<html>
<head>
    <style>${css}</style>
</head>
<body>
    ${html}
</body>
</html>`;
            
            const blob = new Blob([exportContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'grapesjs-export.html';
            a.click();
            URL.revokeObjectURL(url);
        });
        
        // Settings
        document.getElementById('settings-btn').addEventListener('click', function() {
            // Open settings modal
            editor.Modal.setTitle('Settings')
                .setContent('<div style="padding: 20px;">Settings panel coming soon...</div>')
                .open();
        });
        
        // Theme toggle
        document.getElementById('theme-btn').addEventListener('click', function() {
            document.body.classList.toggle('dark-theme');
            const icon = this.querySelector('i');
            icon.classList.toggle('fa-moon');
            icon.classList.toggle('fa-sun');
        });
    }

    // Device selector handler
    function initializeDeviceSelector() {
        const deviceSelect = document.getElementById('device-select');
        deviceSelect.addEventListener('change', function() {
            editor.setDevice(this.value.charAt(0).toUpperCase() + this.value.slice(1));
        });
    }

    // Pages panel handlers
    function initializePagesPanel() {
        // Add page button
        document.getElementById('add-page-btn').addEventListener('click', function() {
            // For now, just show a message
            editor.Modal.setTitle('Add Page')
                .setContent('<div style="padding: 20px;">Add page functionality coming soon...</div>')
                .open();
        });

        // Page menu button
        document.getElementById('page-menu-btn').addEventListener('click', function() {
            // Show page options menu
            editor.Modal.setTitle('Page Options')
                .setContent('<div style="padding: 20px;">Page options coming soon...</div>')
                .open();
        });
    }

    // Tab switching
    function initializeTabSwitching() {
        const stylesTab = document.getElementById('styles-tab');
        const propertiesTab = document.getElementById('properties-tab');
        const styleManager = document.getElementById('style-manager');
        const traitManager = document.getElementById('trait-manager');
        
        stylesTab.addEventListener('click', function() {
            stylesTab.classList.add('active');
            propertiesTab.classList.remove('active');
            styleManager.style.display = 'block';
            traitManager.style.display = 'none';
        });
        
        propertiesTab.addEventListener('click', function() {
            propertiesTab.classList.add('active');
            stylesTab.classList.remove('active');
            styleManager.style.display = 'none';
            traitManager.style.display = 'block';
        });
        
        // Initially show styles
        styleManager.style.display = 'block';
        traitManager.style.display = 'none';
    }

    // Layout control handlers
    function initializeLayoutControls() {
        const layoutBtns = document.querySelectorAll('.layout-btn');
        layoutBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                layoutBtns.forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');
                
                // Apply text alignment based on button
                const selected = editor.getSelected();
                if (selected) {
                    const icon = this.querySelector('i');
                    if (icon.classList.contains('fa-align-left')) {
                        selected.addStyle({ 'text-align': 'left' });
                    } else if (icon.classList.contains('fa-align-center')) {
                        selected.addStyle({ 'text-align': 'center' });
                    } else if (icon.classList.contains('fa-align-right')) {
                        selected.addStyle({ 'text-align': 'right' });
                    } else if (icon.classList.contains('fa-align-justify')) {
                        selected.addStyle({ 'text-align': 'justify' });
                    }
                }
            });
        });
    }

    // Initialize all components
    editor.on('load', function() {
        initializeDefaultContent();
        initializeToolbarHandlers();
        initializeDeviceSelector();
        initializePagesPanel();
        initializeTabSwitching();
        initializeLayoutControls();
        initializeSectionCollapse();

        // Set initial visibility state
        const visibilityBtn = document.getElementById('visibility-btn');
        const canvas = editor.Canvas.getElement();
        canvas.classList.add('gjs-dashed');
        visibilityBtn.classList.add('active');
        visibilityBtn.title = 'Hide Element Borders';

        console.log('GrapesJS Web Builder initialized successfully!');
    });

    // Update selection info when component is selected
    editor.on('component:selected', function(component) {
        const selectionInfo = document.querySelector('.selection-item span:first-of-type');
        const selectionValue = document.querySelector('.selection-value');

        if (component) {
            const tagName = component.get('tagName');
            const className = component.getClasses().join(' ');
            const componentName = component.get('name') || tagName;

            selectionInfo.textContent = componentName.charAt(0).toUpperCase() + componentName.slice(1);
            selectionValue.textContent = className || component.get('type') || 'title';

            // Update layout button states based on component styles
            updateLayoutButtonStates(component);
        }
    });

    // Function to update layout button states
    function updateLayoutButtonStates(component) {
        const layoutBtns = document.querySelectorAll('.layout-btn');
        const textAlign = component.getStyle()['text-align'] || 'left';

        layoutBtns.forEach(btn => btn.classList.remove('active'));

        const alignmentMap = {
            'left': 'fa-align-left',
            'center': 'fa-align-center',
            'right': 'fa-align-right',
            'justify': 'fa-align-justify'
        };

        const targetIcon = alignmentMap[textAlign];
        if (targetIcon) {
            const targetBtn = document.querySelector(`.layout-btn i.${targetIcon}`)?.parentElement;
            if (targetBtn) {
                targetBtn.classList.add('active');
            }
        }
    }

    // Add section collapse/expand functionality
    function initializeSectionCollapse() {
        // Handle style manager section collapse
        editor.on('styleManager:sector:toggle', function(sector) {
            const sectorEl = sector.getEl();
            if (sectorEl) {
                sectorEl.classList.toggle('gjs-sm-open');
            }
        });

        // Handle custom property section collapse
        document.addEventListener('click', function(e) {
            if (e.target.closest('.section-header')) {
                const header = e.target.closest('.section-header');
                const section = header.parentElement;
                const content = section.querySelector('.layout-controls');

                if (content) {
                    const isCollapsed = content.style.display === 'none';
                    content.style.display = isCollapsed ? 'flex' : 'none';
                    header.classList.toggle('collapsed', !isCollapsed);
                }
            }
        });
    }
});
