/* CSS Variables for Theme System */
:root {
    /* Light Theme (Default) */
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;

    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;

    --text-primary: #333333;
    --text-secondary: #666666;
    --text-muted: #999999;

    --border-color: #e0e0e0;
    --border-light: #f0f0f0;

    --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-md: 0 2px 6px rgba(0,0,0,0.15);
    --shadow-lg: 0 4px 12px rgba(0,0,0,0.2);
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;

    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;

    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;

    --border-color: #404040;
    --border-light: #333333;

    --shadow-sm: 0 1px 3px rgba(0,0,0,0.3);
    --shadow-md: 0 2px 6px rgba(0,0,0,0.4);
    --shadow-lg: 0 4px 12px rgba(0,0,0,0.5);
}

/* Blue Theme */
[data-theme="blue"] {
    --primary-color: #2196f3;
    --secondary-color: #607d8b;
    --success-color: #4caf50;
    --danger-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #00bcd4;

    --bg-primary: #f3f8ff;
    --bg-secondary: #e3f2fd;
    --bg-tertiary: #bbdefb;
}

/* Green Theme */
[data-theme="green"] {
    --primary-color: #4caf50;
    --secondary-color: #8bc34a;
    --success-color: #66bb6a;
    --danger-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #00bcd4;

    --bg-primary: #f1f8e9;
    --bg-secondary: #e8f5e8;
    --bg-tertiary: #c8e6c9;
}

/* Purple Theme */
[data-theme="purple"] {
    --primary-color: #9c27b0;
    --secondary-color: #673ab7;
    --success-color: #4caf50;
    --danger-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #00bcd4;

    --bg-primary: #fce4ec;
    --bg-secondary: #f3e5f5;
    --bg-tertiary: #e1bee7;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    overflow: hidden;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Top Toolbar */
.toolbar-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 6px 16px;
    height: 44px;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.toolbar-left {
    display: flex;
    align-items: center;
}

.toolbar-right {
    display: flex;
    gap: 4px;
}

.toolbar-btn {
    background: none;
    border: none;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.toolbar-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.toolbar-btn i {
    font-size: 14px;
}

/* Theme Selector */
.theme-selector {
    position: relative;
    margin-left: 8px;
}

.theme-dropdown {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    color: var(--text-primary);
    cursor: pointer;
    min-width: 80px;
}

.theme-options {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-shadow: var(--shadow-md);
    min-width: 120px;
    z-index: 1001;
    display: none;
}

.theme-options.show {
    display: block;
}

.theme-option {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 12px;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-light);
}

.theme-option:last-child {
    border-bottom: none;
}

.theme-option:hover {
    background-color: var(--bg-tertiary);
}

.theme-option.active {
    background-color: var(--primary-color);
    color: white;
}

/* Main Editor Container */
.editor-container {
    display: flex;
    height: calc(100vh - 50px);
}

/* Left Sidebar */
.sidebar-left {
    width: 250px;
    background-color: #f8f9fa;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

/* Pages Panel */
.pages-panel {
    border-bottom: 1px solid #e0e0e0;
    padding: 8px 12px 12px 12px;
    background-color: #f8f9fa;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.panel-title {
    font-weight: 600;
    color: #333;
    font-size: 13px;
}

.panel-actions {
    display: flex;
    gap: 2px;
}

.btn-icon {
    background: none;
    border: none;
    padding: 3px 5px;
    border-radius: 3px;
    cursor: pointer;
    color: #666;
    font-size: 11px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background-color: #e9ecef;
    color: #333;
}

.device-selector {
    position: relative;
    margin-bottom: 8px;
}

.device-selector select {
    width: 100%;
    padding: 4px 20px 4px 6px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: white;
    font-size: 11px;
    appearance: none;
    color: #333;
}

.device-selector i {
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 8px;
    pointer-events: none;
}

.page-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 6px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    color: #666;
    margin-bottom: 2px;
}

.page-item.active {
    background-color: #e3f2fd;
    color: #1976d2;
}

.page-item i {
    font-size: 11px;
    width: 12px;
}

.page-indicator {
    margin-left: auto;
    font-weight: bold;
    color: #999;
}

/* Layers Panel */
.layers-panel {
    flex: 1;
    padding: 8px 12px;
    background-color: #f8f9fa;
}

.layers-container {
    font-size: 11px;
}

.layers-panel .panel-header {
    margin-bottom: 8px;
}

.layers-panel .panel-title {
    font-size: 13px;
    color: #333;
}

/* Blocks Panel */
.blocks-panel {
    border-bottom: 1px solid #e0e0e0;
    padding: 8px 12px;
    background-color: #f8f9fa;
    max-height: 300px;
    overflow-y: auto;
}

.blocks-container {
    font-size: 11px;
}

.blocks-panel .panel-header {
    margin-bottom: 8px;
}

.blocks-panel .panel-title {
    font-size: 13px;
    color: #333;
}

/* Block styling */
.gjs-block {
    width: auto !important;
    height: auto !important;
    min-height: 40px !important;
    margin: 4px 0 !important;
    padding: 8px !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    background-color: white !important;
    cursor: grab !important;
    transition: all 0.2s ease !important;
}

.gjs-block:hover {
    border-color: #007bff !important;
    box-shadow: 0 2px 4px rgba(0,123,255,0.2) !important;
}

.gjs-block-label {
    font-size: 10px !important;
    color: #666 !important;
    text-align: center !important;
    margin-top: 4px !important;
}

.gjs-block-category {
    margin: 8px 0 4px 0 !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* Templates Panel */
.templates-panel {
    border-bottom: 1px solid var(--border-color);
    padding: 8px 12px;
    background-color: var(--bg-secondary);
    max-height: 250px;
    overflow-y: auto;
}

.templates-container {
    font-size: 11px;
}

.templates-panel .panel-header {
    margin-bottom: 8px;
}

.templates-panel .panel-title {
    font-size: 13px;
    color: var(--text-primary);
}

.template-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin: 4px 0;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-primary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.template-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
    transform: translateY(-1px);
}

.template-preview {
    width: 40px;
    height: 30px;
    background: linear-gradient(135deg, var(--primary-color), var(--bg-tertiary));
    border-radius: 3px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
}

.template-info {
    flex: 1;
}

.template-name {
    font-size: 11px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.template-description {
    font-size: 9px;
    color: var(--text-secondary);
    line-height: 1.2;
}

.template-category {
    margin: 12px 0 4px 0;
    font-size: 10px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid var(--border-light);
    padding-bottom: 2px;
}

/* Canvas Area */
.canvas-area {
    flex: 1;
    background-color: white;
    position: relative;
}

#gjs {
    height: 100%;
    width: 100%;
}

/* GrapesJS Custom Styles */
.gjs-cv-canvas {
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
}

/* Default Content Styles */
.gjs-logo-container {
    text-align: center;
    padding: 30px 20px 15px;
    background-color: white;
}

.gjs-logo {
    height: 32px;
    margin-right: 8px;
    vertical-align: middle;
}

.gjs-logo-container h1 {
    display: inline;
    font-size: 28px;
    font-weight: 400;
    color: #333;
    vertical-align: middle;
    margin: 0;
}

.gjs-content-div {
    max-width: 700px;
    margin: 0 auto;
    padding: 20px 40px;
    background-color: white;
}

.welcome-heading {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 10px 14px;
    border-radius: 4px;
    margin-bottom: 18px;
    font-size: 16px;
    font-weight: 500;
    border: 1px solid #bbdefb;
    line-height: 1.4;
}

.gjs-content-div p {
    margin-bottom: 14px;
    color: #666;
    font-size: 13px;
    line-height: 1.5;
}

.pink-link {
    color: #e91e63;
    text-decoration: none;
    word-break: break-all;
}

.pink-link:hover {
    text-decoration: underline;
}

/* Canvas specific styles */
.gjs-body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: white;
}

.gjs-header {
    background-color: white;
    border-bottom: 1px solid #f0f0f0;
}

/* Right Sidebar */
.sidebar-right {
    width: 280px;
    background-color: #f8f9fa;
    border-left: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

/* Tabs */
.tabs-container {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

.tab-btn {
    flex: 1;
    padding: 10px 12px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-btn.active {
    color: #333;
    border-bottom-color: #007bff;
    background-color: white;
}

.tab-btn:hover:not(.active) {
    background-color: #e9ecef;
}

/* Selection Info */
.selection-info {
    padding: 8px 12px;
    border-bottom: 1px solid #e0e0e0;
    background-color: white;
}

.selection-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: #333;
}

.selection-item i {
    color: #666;
    font-size: 12px;
    width: 14px;
}

.selection-value {
    margin-left: auto;
    color: #999;
    font-size: 10px;
    font-style: italic;
}

/* Property Sections */
.property-section {
    border-bottom: 1px solid #e0e0e0;
    background-color: white;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.section-header:hover {
    background-color: #e9ecef;
}

.section-title {
    font-weight: 500;
    font-size: 11px;
    color: #333;
}

.section-header i {
    font-size: 9px;
    color: #666;
    transition: transform 0.2s ease;
}

.section-header.collapsed i {
    transform: rotate(-90deg);
}

/* Layout Controls */
.layout-controls {
    display: flex;
    gap: 3px;
    padding: 8px 12px;
    background-color: white;
}

.layout-btn {
    flex: 1;
    padding: 6px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
    color: #666;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 28px;
    transition: all 0.2s ease;
}

.layout-btn:hover {
    background-color: #f0f0f0;
    border-color: #ccc;
}

.layout-btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.layout-btn i {
    font-size: 10px;
}

/* Style and Trait Manager Containers */
.style-manager-container,
.trait-manager-container,
.global-styles-container {
    flex: 1;
}

/* Global Styles Panel */
.global-styles-container {
    background-color: var(--bg-primary);
    padding: 12px;
}

.global-styles-section {
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    overflow: hidden;
}

.global-styles-header {
    background-color: var(--bg-secondary);
    padding: 10px 12px;
    border-bottom: 1px solid var(--border-color);
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.global-styles-header:hover {
    background-color: var(--bg-tertiary);
}

.global-styles-content {
    padding: 12px;
    background-color: var(--bg-primary);
}

.global-style-group {
    margin-bottom: 15px;
}

.global-style-label {
    display: block;
    font-size: 11px;
    color: var(--text-secondary);
    margin-bottom: 4px;
    font-weight: 500;
}

.global-style-input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 11px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: border-color 0.2s ease;
}

.global-style-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.global-style-row {
    display: flex;
    gap: 8px;
    align-items: center;
}

.global-style-row .global-style-input {
    flex: 1;
}

.color-picker-wrapper {
    position: relative;
    width: 30px;
    height: 30px;
}

.color-picker {
    width: 100%;
    height: 100%;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    background: transparent;
}

.global-style-preset {
    display: flex;
    gap: 4px;
    margin-top: 8px;
}

.preset-item {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    cursor: pointer;
    border: 1px solid var(--border-color);
    transition: transform 0.2s ease;
}

.preset-item:hover {
    transform: scale(1.1);
}

.global-style-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.global-style-button:hover {
    background: var(--primary-color);
    opacity: 0.9;
}

.global-style-button.secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.global-style-button.secondary:hover {
    background: var(--border-color);
}

/* Context Menu */
.context-menu {
    position: fixed;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: var(--shadow-lg);
    z-index: 10000;
    min-width: 180px;
    padding: 4px 0;
    display: none;
}

.context-menu-item {
    padding: 8px 16px;
    font-size: 12px;
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s ease;
}

.context-menu-item:hover {
    background-color: var(--bg-tertiary);
}

.context-menu-item.disabled {
    color: var(--text-muted);
    cursor: not-allowed;
}

.context-menu-item.disabled:hover {
    background-color: transparent;
}

.context-menu-separator {
    height: 1px;
    background-color: var(--border-light);
    margin: 4px 0;
}

.context-menu-icon {
    width: 14px;
    text-align: center;
    font-size: 11px;
}

.context-menu-shortcut {
    margin-left: auto;
    font-size: 10px;
    color: var(--text-muted);
}

/* GrapesJS Theme Overrides */
.gjs-one-bg {
    background-color: #f8f9fa !important;
}

.gjs-two-color {
    color: #666 !important;
}

.gjs-three-bg {
    background-color: #007bff !important;
    color: white !important;
}

.gjs-four-color {
    color: #007bff !important;
}

/* Selection highlighting */
.gjs-selected {
    outline: 2px solid #007bff !important;
    outline-offset: 1px !important;
}

/* Canvas frame styling */
.gjs-cv-canvas {
    background-color: white !important;
}

/* Component hover effects */
.gjs-comp-selected {
    outline: 2px solid #007bff !important;
}

.gjs-comp-hovered {
    outline: 1px dashed #007bff !important;
}

/* Visibility toggle styles */
.gjs-dashed * {
    outline: 1px dashed rgba(170, 170, 170, 0.7) !important;
}

.gjs-dashed *:hover {
    outline: 2px solid #007bff !important;
}

/* Canvas frame visibility */
.gjs-cv-canvas.gjs-dashed iframe {
    pointer-events: auto !important;
}

/* Ensure visibility toggle button states */
.toolbar-btn.active {
    background-color: #007bff !important;
    color: white !important;
}

/* Layer Manager Styling */
.gjs-lm-layer {
    font-size: 11px !important;
    margin-bottom: 1px !important;
}

.gjs-lm-title {
    padding: 3px 6px !important;
    border-radius: 3px !important;
    color: #666 !important;
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
    position: relative !important;
}

.gjs-lm-title:hover {
    background-color: #f0f0f0 !important;
}

.gjs-lm-layer.gjs-lm-selected > .gjs-lm-title {
    background-color: #e3f2fd !important;
    color: #1976d2 !important;
    font-weight: 500 !important;
}

.gjs-lm-caret {
    width: 8px !important;
    height: 8px !important;
    margin-right: 2px !important;
}

.gjs-lm-title-c {
    flex: 1 !important;
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
}

.gjs-lm-layer-name {
    font-size: 11px !important;
}

/* Layer visibility toggle */
.gjs-lm-vis {
    width: 16px !important;
    height: 16px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    border-radius: 2px !important;
    margin-left: auto !important;
    opacity: 0.6 !important;
    transition: all 0.2s ease !important;
}

.gjs-lm-vis:hover {
    opacity: 1 !important;
    background-color: rgba(0,0,0,0.1) !important;
}

.gjs-lm-vis::before {
    content: '👁' !important;
    font-size: 10px !important;
}

/* Hidden layer styling */
.gjs-lm-layer.gjs-lm-hide .gjs-lm-vis::before {
    content: '🙈' !important;
    opacity: 0.4 !important;
}

.gjs-lm-layer.gjs-lm-hide .gjs-lm-title {
    opacity: 0.5 !important;
    text-decoration: line-through !important;
}

.gjs-lm-layer.gjs-lm-hide .gjs-lm-layer-name {
    color: #999 !important;
}

.gjs-lm-children {
    padding-left: 12px !important;
}

/* Custom layer icons */
.gjs-lm-title::before {
    content: '';
    width: 12px;
    height: 12px;
    background-size: contain;
    background-repeat: no-repeat;
    margin-right: 4px;
}

/* Body layer icon */
.gjs-lm-layer[data-tag="body"] .gjs-lm-title::before {
    content: '📄';
    font-size: 10px;
}

/* Header layer icon */
.gjs-lm-layer[data-tag="header"] .gjs-lm-title::before {
    content: '📋';
    font-size: 10px;
}

/* Div layer icon */
.gjs-lm-layer[data-tag="div"] .gjs-lm-title::before {
    content: '📦';
    font-size: 10px;
}

/* Heading layer icon */
.gjs-lm-layer[data-tag="h1"],
.gjs-lm-layer[data-tag="h2"],
.gjs-lm-layer[data-tag="h3"] .gjs-lm-title::before {
    content: '📝';
    font-size: 10px;
}

/* Text layer icon */
.gjs-lm-layer[data-tag="span"],
.gjs-lm-layer[data-tag="p"] .gjs-lm-title::before {
    content: '📄';
    font-size: 10px;
}

/* Style Manager Styling */
.gjs-sm-sector {
    border-bottom: 1px solid #e0e0e0 !important;
    background-color: white !important;
}

.gjs-sm-title {
    font-size: 11px !important;
    font-weight: 500 !important;
    padding: 8px 12px !important;
    background-color: #f8f9fa !important;
    color: #333 !important;
    cursor: pointer !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    border-bottom: 1px solid #e0e0e0 !important;
}

.gjs-sm-title:hover {
    background-color: #e9ecef !important;
}

.gjs-sm-title::after {
    content: '▼' !important;
    font-size: 8px !important;
    color: #666 !important;
    transition: transform 0.2s ease !important;
}

.gjs-sm-sector.gjs-sm-open .gjs-sm-title::after {
    transform: rotate(180deg) !important;
}

.gjs-sm-properties {
    padding: 8px 12px !important;
    background-color: white !important;
}

.gjs-sm-property {
    margin-bottom: 6px !important;
}

.gjs-sm-label {
    font-size: 10px !important;
    color: #666 !important;
    margin-bottom: 2px !important;
    display: block !important;
}

.gjs-field {
    font-size: 10px !important;
    padding: 3px 5px !important;
    border: 1px solid #ddd !important;
    border-radius: 3px !important;
    width: 100% !important;
    background-color: white !important;
}

.gjs-field:focus {
    border-color: #007bff !important;
    outline: none !important;
}

/* Collapsed sections by default */
.gjs-sm-sector:not(.gjs-sm-open) .gjs-sm-properties {
    display: none !important;
}

.gjs-sm-sector:not(.gjs-sm-open) .gjs-sm-title::after {
    transform: rotate(-90deg) !important;
}
