# GrapesJS Web Builder - Deployment Guide

This guide explains how to deploy the GrapesJS Web Builder application on an nginx web server.

## File Structure

The application consists of the following files:
```
grapesjs-builder/
├── index.html          # Main HTML file
├── css/
│   └── style.css       # Custom styles
├── js/
│   └── app.js          # Application JavaScript
├── nginx.conf          # Nginx configuration
├── DEPLOYMENT.md       # This file
└── readme.md          # Project requirements
```

## Prerequisites

- Nginx web server installed
- Basic knowledge of nginx configuration
- Access to your web server's configuration directory

## Deployment Steps

### 1. Upload Files

Upload all application files to your web server. Common locations:
- **Ubuntu/Debian**: `/var/www/grapesjs-builder/`
- **CentOS/RHEL**: `/var/www/html/grapesjs-builder/`
- **Custom**: Any directory accessible by nginx

### 2. Set Permissions

Ensure nginx can read the files:
```bash
sudo chown -R www-data:www-data /var/www/grapesjs-builder/
sudo chmod -R 755 /var/www/grapesjs-builder/
```

### 3. Configure Nginx

#### Option A: New Site (Recommended)
1. Copy the provided `nginx.conf` to nginx sites directory:
   ```bash
   sudo cp nginx.conf /etc/nginx/sites-available/grapesjs-builder
   ```

2. Edit the configuration file:
   ```bash
   sudo nano /etc/nginx/sites-available/grapesjs-builder
   ```

3. Update the following values:
   - `server_name`: Replace `your-domain.com` with your actual domain
   - `root`: Adjust the path to match your file location

4. Enable the site:
   ```bash
   sudo ln -s /etc/nginx/sites-available/grapesjs-builder /etc/nginx/sites-enabled/
   ```

#### Option B: Add to Existing Site
Add a location block to your existing nginx configuration:
```nginx
location /grapesjs-builder/ {
    alias /var/www/grapesjs-builder/;
    try_files $uri $uri/ /grapesjs-builder/index.html;
}
```

### 4. Test Configuration

Test the nginx configuration:
```bash
sudo nginx -t
```

### 5. Reload Nginx

If the test passes, reload nginx:
```bash
sudo systemctl reload nginx
```

## Access the Application

- **New Site**: Visit `http://your-domain.com`
- **Subdirectory**: Visit `http://your-domain.com/grapesjs-builder/`

## Features

The deployed application includes:

- ✅ **Complete GrapesJS Editor**: Full-featured web page builder
- ✅ **Responsive Design**: Works on desktop, tablet, and mobile
- ✅ **Layer Management**: Visual component hierarchy
- ✅ **Style Manager**: Complete CSS styling controls
- ✅ **Component Library**: Drag-and-drop components
- ✅ **Export Functionality**: Download created pages
- ✅ **Local Storage**: Automatic saving of projects

## Customization

### Branding
- Replace the GrapesJS logo in the header
- Modify colors in `css/style.css`
- Update the welcome message in `index.html`

### Functionality
- Add custom components in `js/app.js`
- Integrate with backend APIs for saving/loading
- Add user authentication if needed

## SSL/HTTPS Setup (Recommended)

For production use, enable HTTPS:

1. Obtain SSL certificates (Let's Encrypt, commercial CA, etc.)
2. Uncomment and configure the HTTPS server block in `nginx.conf`
3. Update certificate paths
4. Enable HTTP to HTTPS redirect

## Troubleshooting

### Common Issues

1. **404 Error**: Check file paths and nginx root directory
2. **Permission Denied**: Verify file ownership and permissions
3. **CSS/JS Not Loading**: Check nginx configuration for static file handling
4. **CORS Issues**: Ensure proper headers are set for external resources

### Logs

Check nginx logs for errors:
```bash
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

## Performance Optimization

The nginx configuration includes:
- Gzip compression for faster loading
- Static file caching
- Security headers
- Optimized SSL settings (when enabled)

## Support

For issues with:
- **GrapesJS**: Visit [GrapesJS Documentation](https://grapesjs.com/docs/)
- **Nginx**: Check [Nginx Documentation](https://nginx.org/en/docs/)
- **This Application**: Review the code in `js/app.js` and `css/style.css`

## Updates

To update the application:
1. Backup your current files
2. Replace files with new versions
3. Test the configuration
4. Reload nginx

The application uses CDN links for GrapesJS core files, so updates to the core library happen automatically.
